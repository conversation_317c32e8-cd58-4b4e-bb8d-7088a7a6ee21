

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pg_trgm" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."administration_route" AS ENUM (
    'intramuscular',
    'subcutaneous',
    'intradermal',
    'oral',
    'nasal'
);


ALTER TYPE "public"."administration_route" OWNER TO "postgres";


CREATE TYPE "public"."administration_site" AS ENUM (
    'left_arm',
    'right_arm',
    'left_thigh',
    'right_thigh',
    'other'
);


ALTER TYPE "public"."administration_site" OWNER TO "postgres";


CREATE TYPE "public"."alert_severity" AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE "public"."alert_severity" OWNER TO "postgres";


CREATE TYPE "public"."alert_status" AS ENUM (
    'active',
    'inactive',
    'resolved'
);


ALTER TYPE "public"."alert_status" OWNER TO "postgres";


CREATE TYPE "public"."allergy_severity" AS ENUM (
    'mild',
    'moderate',
    'severe',
    'life_threatening'
);


ALTER TYPE "public"."allergy_severity" OWNER TO "postgres";


CREATE TYPE "public"."allergy_status" AS ENUM (
    'active',
    'inactive',
    'resolved'
);


ALTER TYPE "public"."allergy_status" OWNER TO "postgres";


CREATE TYPE "public"."appointment_status" AS ENUM (
    'scheduled',
    'checked_in',
    'in_progress',
    'completed',
    'cancelled',
    'no_show'
);


ALTER TYPE "public"."appointment_status" OWNER TO "postgres";


CREATE TYPE "public"."conversation_type" AS ENUM (
    'direct',
    'group',
    'department',
    'announcement'
);


ALTER TYPE "public"."conversation_type" OWNER TO "postgres";


CREATE TYPE "public"."department_type" AS ENUM (
    'primary_care',
    'pediatrics',
    'cardiology',
    'neurology',
    'orthopedics',
    'emergency',
    'laboratory',
    'pharmacy',
    'radiology',
    'billing',
    'administration'
);


ALTER TYPE "public"."department_type" OWNER TO "postgres";


CREATE TYPE "public"."gender" AS ENUM (
    'male',
    'female',
    'other',
    'prefer_not_to_say'
);


ALTER TYPE "public"."gender" OWNER TO "postgres";


CREATE TYPE "public"."material_format" AS ENUM (
    'text',
    'pdf',
    'video',
    'audio',
    'interactive'
);


ALTER TYPE "public"."material_format" OWNER TO "postgres";


CREATE TYPE "public"."message_state" AS ENUM (
    'sent',
    'delivered',
    'read'
);


ALTER TYPE "public"."message_state" OWNER TO "postgres";


CREATE TYPE "public"."notification_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE "public"."notification_priority" OWNER TO "postgres";


CREATE TYPE "public"."notification_status" AS ENUM (
    'pending',
    'sent',
    'delivered',
    'read',
    'failed'
);


ALTER TYPE "public"."notification_status" OWNER TO "postgres";


CREATE TYPE "public"."notification_type" AS ENUM (
    'appointment_reminder',
    'lab_result',
    'prescription_update',
    'medical_record_update',
    'task_assignment',
    'message',
    'alert',
    'system_update'
);


ALTER TYPE "public"."notification_type" OWNER TO "postgres";


CREATE TYPE "public"."order_priority" AS ENUM (
    'routine',
    'urgent',
    'stat',
    'emergency'
);


ALTER TYPE "public"."order_priority" OWNER TO "postgres";


CREATE TYPE "public"."order_status" AS ENUM (
    'pending',
    'approved',
    'in_progress',
    'completed',
    'cancelled',
    'declined'
);


ALTER TYPE "public"."order_status" OWNER TO "postgres";


CREATE TYPE "public"."order_type" AS ENUM (
    'lab',
    'imaging',
    'medication',
    'procedure',
    'referral',
    'consultation',
    'other'
);


ALTER TYPE "public"."order_type" OWNER TO "postgres";


CREATE TYPE "public"."provider_type" AS ENUM (
    'doctor',
    'nurse',
    'specialist',
    'admin'
);


ALTER TYPE "public"."provider_type" OWNER TO "postgres";


CREATE TYPE "public"."referral_priority" AS ENUM (
    'routine',
    'urgent',
    'emergency'
);


ALTER TYPE "public"."referral_priority" OWNER TO "postgres";


CREATE TYPE "public"."referral_status" AS ENUM (
    'pending',
    'scheduled',
    'completed',
    'cancelled',
    'declined'
);


ALTER TYPE "public"."referral_status" OWNER TO "postgres";


CREATE TYPE "public"."task_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE "public"."task_priority" OWNER TO "postgres";


CREATE TYPE "public"."task_status" AS ENUM (
    'pending',
    'in_progress',
    'completed',
    'cancelled',
    'blocked'
);


ALTER TYPE "public"."task_status" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'system_admin',
    'org_admin',
    'clinical_admin',
    'physician',
    'nurse_practitioner',
    'registered_nurse',
    'medical_assistant',
    'front_desk',
    'billing_staff',
    'pharmacist',
    'lab_technician',
    'patient'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE TYPE "public"."workflow_status" AS ENUM (
    'pending',
    'in_progress',
    'completed',
    'cancelled',
    'failed'
);


ALTER TYPE "public"."workflow_status" OWNER TO "postgres";


CREATE TYPE "public"."workflow_trigger" AS ENUM (
    'scheduled',
    'event_based',
    'manual'
);


ALTER TYPE "public"."workflow_trigger" OWNER TO "postgres";


CREATE TYPE "public"."workflow_type" AS ENUM (
    'appointment_reminder',
    'lab_result_notification',
    'prescription_renewal',
    'patient_followup',
    'referral_management',
    'insurance_verification',
    'document_review'
);


ALTER TYPE "public"."workflow_type" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_lab_result_id UUID;
BEGIN
    INSERT INTO lab_results (
        patient_id,
        provider_id,
        test_name,
        test_date,
        results,
        normal_range,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_test_name,
        CURRENT_TIMESTAMP,
        p_results,
        p_normal_range,
        p_notes
    ) RETURNING id INTO v_lab_result_id;

    RETURN v_lab_result_id;
END;
$$;


ALTER FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb" DEFAULT '{"files": []}'::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_record_id UUID;
BEGIN
    INSERT INTO medical_records (
        patient_id,
        provider_id,
        visit_date,
        chief_complaint,
        diagnosis,
        treatment_plan,
        notes,
        attachments
    ) VALUES (
        p_patient_id,
        p_provider_id,
        CURRENT_TIMESTAMP,
        p_chief_complaint,
        p_diagnosis,
        p_treatment_plan,
        p_notes,
        p_attachments
    ) RETURNING id INTO v_record_id;

    RETURN v_record_id;
END;
$$;


ALTER FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority" DEFAULT 'medium'::"public"."task_priority", "p_due_date" timestamp with time zone DEFAULT NULL::timestamp with time zone, "p_related_to" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_task_id UUID;
    v_org_id UUID;
    v_dept_id UUID;
BEGIN
    -- Get organization and department ID from assigned user's role
    SELECT organization_id, department_id INTO v_org_id, v_dept_id
    FROM user_roles
    WHERE user_id = p_assigned_to
    LIMIT 1;

    -- Create task
    INSERT INTO tasks (
        organization_id,
        department_id,
        title,
        description,
        priority,
        assigned_to,
        assigned_by,
        due_date,
        related_to,
        metadata
    ) VALUES (
        v_org_id,
        v_dept_id,
        p_title,
        p_description,
        p_priority,
        p_assigned_to,
        auth.uid(),
        p_due_date,
        COALESCE(p_related_to, '{}'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_task_id;

    -- Send notification to assigned user
    PERFORM send_notification(
        'task_assignment'::notification_type,
        p_assigned_to,
        'New Task Assigned: ' || p_title,
        p_description,
        jsonb_build_object('task_id', v_task_id)
    );

    RETURN v_task_id;
END;
$$;


ALTER FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."audit_log_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    old_data JSONB;
    new_data JSONB;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        old_data = to_jsonb(OLD);
        new_data = NULL;
    ELSIF (TG_OP = 'UPDATE') THEN
        old_data = to_jsonb(OLD);
        new_data = to_jsonb(NEW);
    ELSE
        old_data = NULL;
        new_data = to_jsonb(NEW);
    END IF;

    INSERT INTO audit_logs (
        table_name,
        record_id,
        action,
        old_data,
        new_data,
        changed_by,
        timestamp
    ) VALUES (
        TG_TABLE_NAME,
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.id
            ELSE NEW.id
        END,
        TG_OP,
        old_data,
        new_data,
        auth.uid(),
        CURRENT_TIMESTAMP
    );

    IF (TG_OP = 'DELETE') THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$;


ALTER FUNCTION "public"."audit_log_changes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb" DEFAULT '{}'::"jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_role user_role;
    v_permissions JSONB;
BEGIN
    -- Get user's role and custom permissions
    SELECT role, custom_permissions
    INTO v_role, v_permissions
    FROM user_roles
    WHERE user_id = p_user_id;

    -- Check if user has permission through role
    RETURN EXISTS (
        SELECT 1
        FROM role_permissions
        WHERE role = v_role
        AND resource = p_resource
        AND p_action = ANY(actions)
        AND (
            conditions @> p_context
            OR
            custom_permissions->p_resource->p_action = 'true'
        )
    );
END;
$$;


ALTER FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."disable_rls"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Set session variable to disable RLS checks for this session only
  PERFORM set_config('app.bypass_rls', 'true', true);
END;
$$;


ALTER FUNCTION "public"."disable_rls"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid" DEFAULT NULL::"uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("total_appointments" integer, "completed_appointments" integer, "cancelled_appointments" integer, "avg_duration" numeric, "most_common_reason" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH appointment_stats AS (
        SELECT 
            COUNT(*) as total,
            COUNT(*) FILTER (WHERE status = 'completed') as completed,
            COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
            AVG(duration_minutes) as avg_dur,
            MODE() WITHIN GROUP (ORDER BY reason) as common_reason
        FROM appointments
        WHERE 
            appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND (p_provider_id IS NULL OR provider_id = p_provider_id)
    )
    SELECT 
        total,
        completed,
        cancelled,
        ROUND(avg_dur::DECIMAL, 2),
        common_reason
    FROM appointment_stats;
END;
$$;


ALTER FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid" DEFAULT NULL::"uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval)) RETURNS TABLE("medication_name" "text", "total_prescriptions" integer, "active_prescriptions" integer, "avg_duration_days" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.medication_name,
        COUNT(*)::INTEGER as total_prescriptions,
        COUNT(*) FILTER (WHERE m.active = true)::INTEGER as active_prescriptions,
        AVG(
            CASE 
                WHEN m.end_date IS NOT NULL THEN 
                    (m.end_date - m.start_date)
                ELSE
                    (CURRENT_DATE - m.start_date)
            END
        )::INTEGER as avg_duration_days
    FROM medications m
    WHERE 
        m.start_date >= p_start_date
        AND (p_provider_id IS NULL OR m.provider_id = p_provider_id)
    GROUP BY m.medication_name
    ORDER BY total_prescriptions DESC;
END;
$$;


ALTER FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" timestamp with time zone DEFAULT CURRENT_DATE) RETURNS TABLE("metric_name" "text", "current_value" numeric, "previous_value" numeric, "change_percentage" numeric)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH current_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
    ),
    previous_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
    )
    SELECT 
        c.metric_name,
        c.value as current_value,
        p.value as previous_value,
        CASE 
            WHEN p.value = 0 THEN NULL
            ELSE ((c.value - p.value) / p.value * 100)
        END as change_percentage
    FROM current_period c
    LEFT JOIN previous_period p ON c.metric_name = p.metric_name;
END;
$$;


ALTER FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("age_group" "text", "gender_distribution" "jsonb", "patient_count" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH patient_ages AS (
        SELECT 
            CASE 
                WHEN age(date_of_birth) < INTERVAL '18 years' THEN '0-17'
                WHEN age(date_of_birth) < INTERVAL '30 years' THEN '18-29'
                WHEN age(date_of_birth) < INTERVAL '50 years' THEN '30-49'
                WHEN age(date_of_birth) < INTERVAL '70 years' THEN '50-69'
                ELSE '70+'
            END as age_group,
            gender
        FROM patients p
        WHERE p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        )
    )
    SELECT 
        age_group,
        jsonb_object_agg(gender, COUNT(*)::INTEGER) as gender_distribution,
        COUNT(*)::INTEGER as patient_count
    FROM patient_ages
    GROUP BY age_group
    ORDER BY age_group;
END;
$$;


ALTER FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") RETURNS TABLE("patient_info" "jsonb", "recent_appointments" "jsonb", "active_medications" "jsonb", "recent_lab_results" "jsonb", "recent_medical_records" "jsonb")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH patient_data AS (
        SELECT 
            jsonb_build_object(
                'id', id,
                'first_name', first_name,
                'last_name', last_name,
                'date_of_birth', date_of_birth,
                'gender', gender,
                'phone', phone,
                'email', email,
                'medical_history', medical_history
            ) as patient_info,
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', a.id,
                        'date', a.appointment_date,
                        'provider', (
                            SELECT jsonb_build_object(
                                'id', hp.id,
                                'name', hp.first_name || ' ' || hp.last_name,
                                'type', hp.provider_type
                            )
                            FROM healthcare_providers hp
                            WHERE hp.id = a.provider_id
                        ),
                        'status', a.status,
                        'reason', a.reason
                    )
                )
                FROM appointments a
                WHERE a.patient_id = p.id
                AND a.appointment_date >= CURRENT_DATE - INTERVAL '6 months'
                ORDER BY a.appointment_date DESC
                LIMIT 5
            ) as recent_appointments,
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', m.id,
                        'name', m.medication_name,
                        'dosage', m.dosage,
                        'frequency', m.frequency,
                        'start_date', m.start_date,
                        'end_date', m.end_date,
                        'instructions', m.instructions
                    )
                )
                FROM medications m
                WHERE m.patient_id = p.id
                AND m.active = true
                ORDER BY m.start_date DESC
            ) as active_medications,
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', lr.id,
                        'test_name', lr.test_name,
                        'test_date', lr.test_date,
                        'results', lr.results,
                        'normal_range', lr.normal_range
                    )
                )
                FROM lab_results lr
                WHERE lr.patient_id = p.id
                ORDER BY lr.test_date DESC
                LIMIT 10
            ) as recent_lab_results,
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', mr.id,
                        'visit_date', mr.visit_date,
                        'chief_complaint', mr.chief_complaint,
                        'diagnosis', mr.diagnosis,
                        'treatment_plan', mr.treatment_plan
                    )
                )
                FROM medical_records mr
                WHERE mr.patient_id = p.id
                ORDER BY mr.visit_date DESC
                LIMIT 5
            ) as recent_medical_records
        FROM patients p
        WHERE p.id = p_patient_id
    )
    SELECT 
        patient_info,
        COALESCE(recent_appointments, '[]'::jsonb),
        COALESCE(active_medications, '[]'::jsonb),
        COALESCE(recent_lab_results, '[]'::jsonb),
        COALESCE(recent_medical_records, '[]'::jsonb)
    FROM patient_data;
END;
$$;


ALTER FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '1 year'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("visit_date" "date", "visit_type" "text", "provider_name" "text", "diagnosis" "text"[], "medications_prescribed" "text"[], "lab_tests_performed" "text"[])
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH visit_data AS (
        SELECT 
            mr.visit_date::DATE,
            'Medical Record'::TEXT as visit_type,
            hp.first_name || ' ' || hp.last_name as provider_name,
            mr.diagnosis,
            ARRAY(
                SELECT m.medication_name
                FROM medications m
                WHERE m.patient_id = mr.patient_id
                AND m.start_date::DATE = mr.visit_date::DATE
            ) as medications,
            ARRAY(
                SELECT lr.test_name
                FROM lab_results lr
                WHERE lr.patient_id = mr.patient_id
                AND lr.test_date::DATE = mr.visit_date::DATE
            ) as lab_tests
        FROM medical_records mr
        JOIN healthcare_providers hp ON hp.id = mr.provider_id
        WHERE 
            mr.patient_id = p_patient_id
            AND mr.visit_date::DATE BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            a.appointment_date::DATE,
            'Appointment'::TEXT,
            hp.first_name || ' ' || hp.last_name,
            NULL::TEXT[],
            NULL::TEXT[],
            NULL::TEXT[]
        FROM appointments a
        JOIN healthcare_providers hp ON hp.id = a.provider_id
        WHERE 
            a.patient_id = p_patient_id
            AND a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND NOT EXISTS (
                SELECT 1 
                FROM medical_records mr 
                WHERE mr.patient_id = a.patient_id 
                AND mr.visit_date::DATE = a.appointment_date::DATE
            )
    )
    SELECT * FROM visit_data
    ORDER BY visit_date DESC;
END;
$$;


ALTER FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_provider_workload"("p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("provider_id" "uuid", "provider_name" "text", "provider_type" "public"."provider_type", "total_appointments" integer, "total_patients" integer, "avg_appointments_per_day" numeric)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH provider_stats AS (
        SELECT 
            hp.id,
            hp.first_name || ' ' || hp.last_name as name,
            hp.provider_type,
            COUNT(DISTINCT a.id) as appointment_count,
            COUNT(DISTINCT a.patient_id) as patient_count,
            COUNT(DISTINCT a.id)::DECIMAL / 
                GREATEST(1, (p_end_date - p_start_date + 1)) as avg_daily_appointments
        FROM healthcare_providers hp
        LEFT JOIN appointments a ON 
            a.provider_id = hp.id AND 
            a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
        GROUP BY hp.id, hp.first_name, hp.last_name, hp.provider_type
    )
    SELECT 
        id,
        name,
        provider_type,
        appointment_count,
        patient_count,
        ROUND(avg_daily_appointments, 2)
    FROM provider_stats
    ORDER BY appointment_count DESC;
END;
$$;


ALTER FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_departments"("p_user_id" "uuid") RETURNS TABLE("department_id" "uuid")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT d.id
    FROM departments d
    JOIN user_roles ur ON ur.department_id = d.id
    WHERE ur.user_id = p_user_id;
END;
$$;


ALTER FUNCTION "public"."get_user_departments"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM medical_records
        WHERE provider_id = provider_id
        AND patient_id = patient_id
    );
END;
$$;


ALTER FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM healthcare_providers
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_patient"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM patients
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."is_patient"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_message_read"("p_message_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Update message status
    UPDATE message_states
    SET 
        state = 'read',
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        message_id = p_message_id
        AND user_id = auth.uid()
        AND state != 'read';

    -- Update last read timestamp in conversation
    UPDATE conversation_participants
    SET last_read_at = CURRENT_TIMESTAMP
    WHERE 
        conversation_id = (SELECT conversation_id FROM messages WHERE id = p_message_id)
        AND user_id = auth.uid();

    RETURN FOUND;
END;
$$;


ALTER FUNCTION "public"."mark_message_read"("p_message_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    UPDATE notifications
    SET 
        status = 'read'::notification_status,
        read_at = CURRENT_TIMESTAMP
    WHERE 
        id = p_notification_id
        AND recipient_id = auth.uid()
        AND status = 'delivered'::notification_status;
    
    RETURN FOUND;
END;
$$;


ALTER FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_medication_id UUID;
BEGIN
    INSERT INTO medications (
        patient_id,
        provider_id,
        medication_name,
        dosage,
        frequency,
        start_date,
        end_date,
        instructions,
        active
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_medication_name,
        p_dosage,
        p_frequency,
        p_start_date,
        p_end_date,
        p_instructions,
        TRUE
    ) RETURNING id INTO v_medication_id;

    RETURN v_medication_id;
END;
$$;


ALTER FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") RETURNS "date"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN start_date + (random() * (end_date - start_date))::INTEGER;
END;
$$;


ALTER FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."random_timestamp"("start_date" timestamp without time zone, "end_date" timestamp without time zone) RETURNS timestamp without time zone
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN start_date + (random() * (end_date - start_date));
END;
$$;


ALTER FUNCTION "public"."random_timestamp"("start_date" timestamp without time zone, "end_date" timestamp without time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_org_id UUID;
    v_event_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record event
    INSERT INTO analytics_events (
        organization_id,
        event_type,
        event_data,
        user_id
    ) VALUES (
        v_org_id,
        p_event_type,
        p_event_data,
        auth.uid()
    ) RETURNING id INTO v_event_id;

    RETURN v_event_id;
END;
$$;


ALTER FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_org_id UUID;
    v_metric_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record metric
    INSERT INTO analytics_metrics (
        organization_id,
        metric_name,
        metric_value,
        dimensions
    ) VALUES (
        v_org_id,
        p_metric_name,
        p_metric_value,
        COALESCE(p_dimensions, '{}'::jsonb)
    ) RETURNING id INTO v_metric_id;

    RETURN v_metric_id;
END;
$$;


ALTER FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."refresh_analytics_views"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_daily_appointments;
    REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_provider_metrics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_patient_metrics;
END;
$$;


ALTER FUNCTION "public"."refresh_analytics_views"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_appointment_id UUID;
    v_conflict_count INTEGER;
BEGIN
    -- Check for scheduling conflicts
    SELECT COUNT(*)
    INTO v_conflict_count
    FROM appointments
    WHERE provider_id = p_provider_id
    AND appointment_date < (p_appointment_date + (p_duration_minutes || ' minutes')::INTERVAL)
    AND (appointment_date + (duration_minutes || ' minutes')::INTERVAL) > p_appointment_date;

    IF v_conflict_count > 0 THEN
        RAISE EXCEPTION 'Scheduling conflict detected';
    END IF;

    INSERT INTO appointments (
        patient_id,
        provider_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_appointment_date,
        p_duration_minutes,
        'scheduled'::appointment_status,
        p_reason,
        ''
    ) RETURNING id INTO v_appointment_id;

    RETURN v_appointment_id;
END;
$$;


ALTER FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid" DEFAULT NULL::"uuid", "p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("id" "uuid", "patient_id" "uuid", "provider_id" "uuid", "visit_date" timestamp without time zone, "chief_complaint" "text", "diagnosis" "text"[], "relevance" double precision)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        mr.patient_id,
        mr.provider_id,
        mr.visit_date,
        mr.chief_complaint,
        mr.diagnosis,
        ts_rank(mr.search_vector, to_tsquery('english', search_query)) as relevance
    FROM medical_records mr
    WHERE 
        mr.search_vector @@ to_tsquery('english', search_query)
        AND (p_patient_id IS NULL OR mr.patient_id = p_patient_id)
        AND (p_provider_id IS NULL OR mr.provider_id = p_provider_id)
    ORDER BY relevance DESC;
END;
$$;


ALTER FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("id" "uuid", "first_name" "text", "last_name" "text", "date_of_birth" "date", "gender" "public"."gender", "relevance" double precision)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.date_of_birth,
        p.gender,
        ts_rank(p.search_vector, to_tsquery('english', search_query)) as relevance
    FROM patients p
    WHERE 
        p.search_vector @@ to_tsquery('english', search_query)
        AND (p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        ))
    ORDER BY relevance DESC;
END;
$$;


ALTER FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_message_id UUID;
    v_participant record;
BEGIN
    -- Verify sender is participant
    IF NOT EXISTS (
        SELECT 1 FROM conversation_participants
        WHERE conversation_id = p_conversation_id
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;

    -- Create message
    INSERT INTO messages (
        conversation_id,
        sender_id,
        content,
        attachments,
        metadata
    ) VALUES (
        p_conversation_id,
        auth.uid(),
        p_content,
        COALESCE(p_attachments, '[]'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_message_id;

    -- Create message status for all participants
    FOR v_participant IN 
        SELECT user_id 
        FROM conversation_participants 
        WHERE conversation_id = p_conversation_id 
        AND user_id != auth.uid()
    LOOP
        INSERT INTO message_states (message_id, user_id, state)
        VALUES (v_message_id, v_participant.user_id, 'sent');

        -- Send notification to participant
        PERFORM send_notification(
            'message'::notification_type,
            v_participant.user_id,
            'New Message',
            p_content,
            jsonb_build_object(
                'conversation_id', p_conversation_id,
                'message_id', v_message_id
            )
        );
    END LOOP;

    -- Update conversation timestamp
    UPDATE conversations
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = p_conversation_id;

    RETURN v_message_id;
END;
$$;


ALTER FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb", "p_priority" "public"."notification_priority" DEFAULT 'medium'::"public"."notification_priority", "p_scheduled_for" timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_notification_id UUID;
    v_org_id UUID;
BEGIN
    -- Get organization ID from recipient's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = p_recipient_id
    LIMIT 1;

    -- Create notification
    INSERT INTO notifications (
        organization_id,
        type,
        priority,
        status,
        sender_id,
        recipient_id,
        title,
        content,
        metadata,
        scheduled_for
    ) VALUES (
        v_org_id,
        p_type,
        p_priority,
        CASE 
            WHEN p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP 
            THEN 'sent'::notification_status 
            ELSE 'pending'::notification_status 
        END,
        auth.uid(),
        p_recipient_id,
        p_title,
        p_content,
        COALESCE(p_metadata, '{}'::jsonb),
        p_scheduled_for
    ) RETURNING id INTO v_notification_id;

    -- If notification is for immediate delivery, update sent_at
    IF p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP THEN
        UPDATE notifications 
        SET sent_at = CURRENT_TIMESTAMP 
        WHERE id = v_notification_id;
    END IF;

    RETURN v_notification_id;
END;
$$;


ALTER FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type" DEFAULT 'direct'::"public"."conversation_type", "p_title" "text" DEFAULT NULL::"text", "p_initial_message" "text" DEFAULT NULL::"text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_conversation_id UUID;
    v_org_id UUID;
    v_participant UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Create conversation
    INSERT INTO conversations (
        organization_id,
        type,
        title,
        metadata,
        created_by
    ) VALUES (
        v_org_id,
        p_type,
        COALESCE(p_title, CASE 
            WHEN p_type = 'direct' THEN NULL 
            ELSE 'New Conversation' 
        END),
        COALESCE(p_metadata, '{}'::jsonb),
        auth.uid()
    ) RETURNING id INTO v_conversation_id;

    -- Add participants
    FOREACH v_participant IN ARRAY p_participants || ARRAY[auth.uid()]
    LOOP
        INSERT INTO conversation_participants (conversation_id, user_id)
        VALUES (v_conversation_id, v_participant)
        ON CONFLICT DO NOTHING;
    END LOOP;

    -- Add initial message if provided
    IF p_initial_message IS NOT NULL THEN
        INSERT INTO messages (conversation_id, sender_id, content)
        VALUES (v_conversation_id, auth.uid(), p_initial_message);
    END IF;

    RETURN v_conversation_id;
END;
$$;


ALTER FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_instance_id UUID;
BEGIN
    -- Create workflow instance
    INSERT INTO workflow_instances (
        workflow_id,
        context
    ) VALUES (
        p_workflow_id,
        p_context
    ) RETURNING id INTO v_instance_id;

    -- Log workflow start
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message
    ) VALUES (
        v_instance_id,
        0,
        'workflow_start',
        'in_progress',
        'Workflow started'
    );

    RETURN v_instance_id;
END;
$$;


ALTER FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_medical_record_search_vector"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', coalesce(NEW.chief_complaint, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(array_to_string(NEW.diagnosis, ' '), '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.treatment_plan, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.notes, '')), 'C');
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_medical_record_search_vector"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_patient_search_vector"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', coalesce(NEW.first_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.last_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.medical_history::text, '')), 'B');
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_patient_search_vector"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_task tasks;
BEGIN
    -- Get task details
    SELECT * INTO v_task
    FROM tasks
    WHERE id = p_task_id
    AND (assigned_to = auth.uid() OR assigned_by = auth.uid());

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Update task status
    UPDATE tasks
    SET 
        status = p_status,
        completed_at = CASE WHEN p_status = 'completed' THEN CURRENT_TIMESTAMP ELSE NULL END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_task_id;

    -- Add comment if provided
    IF p_comment IS NOT NULL THEN
        INSERT INTO task_comments (task_id, user_id, content)
        VALUES (p_task_id, auth.uid(), p_comment);
    END IF;

    -- Notify task creator if completed
    IF p_status = 'completed' AND v_task.assigned_by != auth.uid() THEN
        PERFORM send_notification(
            'task_assignment'::notification_type,
            v_task.assigned_by,
            'Task Completed: ' || v_task.title,
            'Task has been marked as completed' || COALESCE(' with comment: ' || p_comment, ''),
            jsonb_build_object('task_id', p_task_id)
        );
    END IF;

    RETURN true;
END;
$$;


ALTER FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb" DEFAULT NULL::"jsonb", "p_message" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_workflow_instance workflow_instances;
    v_workflow workflows;
BEGIN
    -- Get workflow instance
    SELECT * INTO v_workflow_instance
    FROM workflow_instances
    WHERE id = p_instance_id
    FOR UPDATE;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Get workflow
    SELECT * INTO v_workflow
    FROM workflows
    WHERE id = v_workflow_instance.workflow_id;

    -- Update workflow instance
    UPDATE workflow_instances
    SET
        status = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN 'completed'
            WHEN p_status = 'failed' THEN 'failed'
            ELSE 'in_progress'
        END,
        current_step = CASE 
            WHEN p_status = 'completed' THEN p_step_number + 1
            ELSE p_step_number
        END,
        results = results || COALESCE(p_result, '{}'::jsonb),
        completed_at = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN CURRENT_TIMESTAMP
            ELSE completed_at
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_instance_id;

    -- Log step update
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message,
        details
    ) VALUES (
        p_instance_id,
        p_step_number,
        (v_workflow.steps[p_step_number + 1]->>'name'),
        p_status,
        COALESCE(p_message, 'Step ' || p_step_number || ' ' || p_status),
        COALESCE(p_result, '{}'::jsonb)
    );

    RETURN true;
END;
$$;


ALTER FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."allergies" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "allergen" "text" NOT NULL,
    "reaction" "text",
    "severity" "public"."allergy_severity" NOT NULL,
    "onset_date" "date",
    "status" "public"."allergy_status" DEFAULT 'active'::"public"."allergy_status",
    "reported_by" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."allergies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."appointments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "appointment_date" timestamp with time zone NOT NULL,
    "duration_minutes" integer DEFAULT 30 NOT NULL,
    "status" "public"."appointment_status" DEFAULT 'scheduled'::"public"."appointment_status" NOT NULL,
    "reason" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "organization_id" "uuid",
    "department_id" "uuid"
);


ALTER TABLE "public"."appointments" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_daily_appointments" AS
 SELECT "appointments"."organization_id",
    "date_trunc"('day'::"text", "appointments"."appointment_date") AS "date",
    "appointments"."department_id",
    "count"(*) AS "total_appointments",
    "count"(*) FILTER (WHERE ("appointments"."status" = 'completed'::"public"."appointment_status")) AS "completed_appointments",
    "count"(*) FILTER (WHERE ("appointments"."status" = 'cancelled'::"public"."appointment_status")) AS "cancelled_appointments",
    "avg"("appointments"."duration_minutes") AS "avg_duration"
   FROM "public"."appointments"
  GROUP BY "appointments"."organization_id", ("date_trunc"('day'::"text", "appointments"."appointment_date")), "appointments"."department_id"
  WITH NO DATA;


ALTER TABLE "public"."analytics_daily_appointments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."analytics_events" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "event_type" "text" NOT NULL,
    "event_data" "jsonb" NOT NULL,
    "user_id" "uuid",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."analytics_events" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."analytics_metrics" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "metric_name" "text" NOT NULL,
    "metric_value" numeric NOT NULL,
    "dimensions" "jsonb" DEFAULT '{}'::"jsonb",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."analytics_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."medical_records" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "visit_date" timestamp with time zone NOT NULL,
    "chief_complaint" "text",
    "diagnosis" "text"[],
    "treatment_plan" "text",
    "notes" "text",
    "attachments" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "search_vector" "tsvector",
    "organization_id" "uuid",
    "department_id" "uuid"
);


ALTER TABLE "public"."medical_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."medications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "medication_name" "text" NOT NULL,
    "dosage" "text" NOT NULL,
    "frequency" "text" NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "instructions" "text",
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."medications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patients" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "date_of_birth" "date" NOT NULL,
    "gender" "public"."gender" NOT NULL,
    "phone" "text",
    "email" "text",
    "address" "text",
    "emergency_contact" "text",
    "insurance_info" "jsonb",
    "medical_history" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "search_vector" "tsvector",
    "organization_id" "uuid"
);


ALTER TABLE "public"."patients" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_patient_metrics" AS
 SELECT "p"."organization_id",
    "count"(DISTINCT "p"."id") AS "total_patients",
    "count"(DISTINCT
        CASE
            WHEN ("a"."id" IS NOT NULL) THEN "p"."id"
            ELSE NULL::"uuid"
        END) AS "active_patients",
    "avg"(EXTRACT(year FROM "age"(("p"."date_of_birth")::timestamp with time zone))) AS "avg_patient_age",
    "count"(DISTINCT "a"."id") AS "total_appointments",
    "count"(DISTINCT "mr"."id") AS "total_medical_records",
    "count"(DISTINCT "m"."id") AS "total_medications"
   FROM ((("public"."patients" "p"
     LEFT JOIN "public"."appointments" "a" ON (("a"."patient_id" = "p"."id")))
     LEFT JOIN "public"."medical_records" "mr" ON (("mr"."patient_id" = "p"."id")))
     LEFT JOIN "public"."medications" "m" ON (("m"."patient_id" = "p"."id")))
  GROUP BY "p"."organization_id"
  WITH NO DATA;


ALTER TABLE "public"."analytics_patient_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."healthcare_providers" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "provider_type" "public"."provider_type" NOT NULL,
    "specialization" "text",
    "license_number" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "organization_id" "uuid",
    "department_id" "uuid",
    "role" "public"."user_role",
    "specialties" "text"[],
    "credentials" "jsonb",
    "schedule_settings" "jsonb",
    "permissions" "jsonb"
);


ALTER TABLE "public"."healthcare_providers" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_provider_metrics" AS
 SELECT "hp"."organization_id",
    "hp"."id" AS "provider_id",
    (("hp"."first_name" || ' '::"text") || "hp"."last_name") AS "provider_name",
    "hp"."role",
    "count"(DISTINCT "a"."id") AS "total_appointments",
    "count"(DISTINCT "a"."patient_id") AS "unique_patients",
    "count"(DISTINCT "mr"."id") AS "medical_records_created",
    "count"(DISTINCT "m"."id") AS "medications_prescribed"
   FROM ((("public"."healthcare_providers" "hp"
     LEFT JOIN "public"."appointments" "a" ON (("a"."provider_id" = "hp"."id")))
     LEFT JOIN "public"."medical_records" "mr" ON (("mr"."provider_id" = "hp"."id")))
     LEFT JOIN "public"."medications" "m" ON (("m"."provider_id" = "hp"."id")))
  GROUP BY "hp"."organization_id", "hp"."id", "hp"."first_name", "hp"."last_name", "hp"."role"
  WITH NO DATA;


ALTER TABLE "public"."analytics_provider_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."audit_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "table_name" "text" NOT NULL,
    "record_id" "uuid" NOT NULL,
    "action" "text" NOT NULL,
    "old_data" "jsonb",
    "new_data" "jsonb",
    "changed_by" "uuid",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."audit_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."billing_codes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "code" "text" NOT NULL,
    "description" "text" NOT NULL,
    "type" "text" NOT NULL,
    "effective_date" "date" NOT NULL,
    "end_date" "date",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."billing_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."care_team_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "role" "text" NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "primary_contact" boolean DEFAULT false,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_date_range" CHECK ((("end_date" IS NULL) OR ("end_date" >= "start_date")))
);


ALTER TABLE "public"."care_team_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."claims" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "insurance_provider_id" "uuid",
    "service_date" "date" NOT NULL,
    "billing_codes" "jsonb" NOT NULL,
    "status" "text" NOT NULL,
    "amount" numeric NOT NULL,
    "submitted_at" timestamp with time zone,
    "processed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."claims" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."clinical_notes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "medical_record_id" "uuid",
    "note_type" "text" NOT NULL,
    "content" "text" NOT NULL,
    "signed_by" "uuid",
    "signed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."clinical_notes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversation_participants" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid",
    "user_id" "uuid",
    "role" "text" DEFAULT 'member'::"text",
    "last_read_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."conversation_participants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."conversation_type" NOT NULL,
    "title" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."departments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "facility_id" "uuid",
    "name" "text" NOT NULL,
    "type" "public"."department_type" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."departments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "patient_id" "uuid",
    "document_type" "text" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text",
    "file_path" "text" NOT NULL,
    "mime_type" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."education_materials" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "category" "text" NOT NULL,
    "language" "text" DEFAULT 'en'::"text",
    "format" "public"."material_format" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."education_materials" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."facilities" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "address" "jsonb" NOT NULL,
    "contact_info" "jsonb" NOT NULL,
    "operating_hours" "jsonb",
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."facilities" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."immunizations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "vaccine_name" "text" NOT NULL,
    "vaccine_code" "text",
    "dose_number" integer,
    "administered_date" "date" NOT NULL,
    "administered_by" "uuid",
    "manufacturer" "text",
    "lot_number" "text",
    "expiration_date" "date",
    "site" "public"."administration_site",
    "route" "public"."administration_route",
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_dose_number" CHECK (("dose_number" > 0)),
    CONSTRAINT "valid_expiration" CHECK (("expiration_date" >= "administered_date"))
);


ALTER TABLE "public"."immunizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."insurance_providers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "contact_info" "jsonb" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."insurance_providers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inventory_items" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "facility_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "unit" "text" NOT NULL,
    "minimum_quantity" integer,
    "location" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."inventory_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inventory_transactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "item_id" "uuid",
    "transaction_type" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "performed_by" "uuid",
    "reason" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."inventory_transactions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."lab_results" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "test_name" "text" NOT NULL,
    "test_date" timestamp with time zone NOT NULL,
    "results" "jsonb" NOT NULL,
    "normal_range" "jsonb",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."lab_results" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_states" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "message_id" "uuid",
    "user_id" "uuid",
    "state" "public"."message_state" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."message_states" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid",
    "sender_id" "uuid",
    "content" "text" NOT NULL,
    "attachments" "jsonb" DEFAULT '[]'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_preferences" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "email_enabled" boolean DEFAULT true,
    "sms_enabled" boolean DEFAULT false,
    "push_enabled" boolean DEFAULT true,
    "in_app_enabled" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notification_preferences" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_templates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "name" "text" NOT NULL,
    "subject_template" "text" NOT NULL,
    "content_template" "text" NOT NULL,
    "metadata_template" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notification_templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "priority" "public"."notification_priority" DEFAULT 'medium'::"public"."notification_priority" NOT NULL,
    "status" "public"."notification_status" DEFAULT 'pending'::"public"."notification_status" NOT NULL,
    "sender_id" "uuid",
    "recipient_id" "uuid",
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "scheduled_for" timestamp with time zone,
    "sent_at" timestamp with time zone,
    "read_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."orders" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "ordering_provider_id" "uuid",
    "order_type" "public"."order_type" NOT NULL,
    "status" "public"."order_status" DEFAULT 'pending'::"public"."order_status",
    "priority" "public"."order_priority" DEFAULT 'routine'::"public"."order_priority",
    "order_details" "jsonb" NOT NULL,
    "diagnosis_codes" "jsonb",
    "notes" "text",
    "ordered_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "scheduled_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_order_dates" CHECK (((("scheduled_date" IS NULL) OR ("scheduled_date" >= "ordered_at")) AND (("completed_at" IS NULL) OR ("completed_at" >= "ordered_at"))))
);


ALTER TABLE "public"."orders" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "subscription_tier" "text",
    "billing_info" "jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."organizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_alerts" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "alert_type" "text" NOT NULL,
    "description" "text" NOT NULL,
    "severity" "public"."alert_severity" NOT NULL,
    "status" "public"."alert_status" DEFAULT 'active'::"public"."alert_status",
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "created_by" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_date_range" CHECK ((("end_date" IS NULL) OR ("end_date" >= "start_date")))
);


ALTER TABLE "public"."patient_alerts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_education_records" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "material_id" "uuid",
    "provider_id" "uuid",
    "provided_date" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_education_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_portal_settings" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "preferences" "jsonb" DEFAULT '{}'::"jsonb",
    "communication_settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_portal_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_questionnaires" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "questionnaire_type" "text" NOT NULL,
    "responses" "jsonb" NOT NULL,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_questionnaires" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."referrals" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "referring_provider_id" "uuid",
    "referred_to_provider_id" "uuid",
    "reason" "text" NOT NULL,
    "priority" "public"."referral_priority" DEFAULT 'routine'::"public"."referral_priority",
    "status" "public"."referral_status" DEFAULT 'pending'::"public"."referral_status",
    "referral_date" "date" NOT NULL,
    "scheduled_date" "date",
    "completed_date" "date",
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_referral_dates" CHECK (((("scheduled_date" IS NULL) OR ("scheduled_date" >= "referral_date")) AND (("completed_date" IS NULL) OR ("completed_date" >= "referral_date"))))
);


ALTER TABLE "public"."referrals" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."role_permissions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "role" "public"."user_role" NOT NULL,
    "resource" "text" NOT NULL,
    "actions" "text"[] NOT NULL,
    "conditions" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."role_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."task_comments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "task_id" "uuid",
    "user_id" "uuid",
    "content" "text" NOT NULL,
    "attachments" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."task_comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."task_watchers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "task_id" "uuid",
    "user_id" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."task_watchers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tasks" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "department_id" "uuid",
    "title" "text" NOT NULL,
    "description" "text",
    "priority" "public"."task_priority" DEFAULT 'medium'::"public"."task_priority" NOT NULL,
    "status" "public"."task_status" DEFAULT 'pending'::"public"."task_status" NOT NULL,
    "assigned_to" "uuid",
    "assigned_by" "uuid",
    "due_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "related_to" "jsonb" DEFAULT '{}'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."tasks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "department_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "leader_id" "uuid",
    "members" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."teams" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."templates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "content" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "organization_id" "uuid",
    "role" "public"."user_role" NOT NULL,
    "department_id" "uuid",
    "custom_permissions" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vital_signs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "recorded_by" "uuid",
    "recorded_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "blood_pressure_systolic" integer,
    "blood_pressure_diastolic" integer,
    "heart_rate" integer,
    "respiratory_rate" integer,
    "temperature" numeric,
    "temperature_unit" "text" DEFAULT 'C'::"text",
    "oxygen_saturation" integer,
    "height" numeric,
    "weight" numeric,
    "bmi" numeric,
    "pain_level" integer,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_blood_pressure_diastolic" CHECK ((("blood_pressure_diastolic" > 0) AND ("blood_pressure_diastolic" < 200))),
    CONSTRAINT "valid_blood_pressure_systolic" CHECK ((("blood_pressure_systolic" > 0) AND ("blood_pressure_systolic" < 300))),
    CONSTRAINT "valid_heart_rate" CHECK ((("heart_rate" > 0) AND ("heart_rate" < 300))),
    CONSTRAINT "valid_oxygen_saturation" CHECK ((("oxygen_saturation" >= 0) AND ("oxygen_saturation" <= 100))),
    CONSTRAINT "valid_pain_level" CHECK ((("pain_level" >= 0) AND ("pain_level" <= 10))),
    CONSTRAINT "valid_respiratory_rate" CHECK ((("respiratory_rate" > 0) AND ("respiratory_rate" < 100)))
);


ALTER TABLE "public"."vital_signs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflow_instances" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "workflow_id" "uuid",
    "status" "public"."workflow_status" DEFAULT 'pending'::"public"."workflow_status" NOT NULL,
    "context" "jsonb" NOT NULL,
    "current_step" integer DEFAULT 0,
    "results" "jsonb" DEFAULT '[]'::"jsonb",
    "started_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflow_instances" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflow_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "workflow_instance_id" "uuid",
    "step_number" integer NOT NULL,
    "step_name" "text" NOT NULL,
    "status" "public"."workflow_status" NOT NULL,
    "message" "text",
    "details" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflow_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflows" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."workflow_type" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "trigger_type" "public"."workflow_trigger" NOT NULL,
    "trigger_config" "jsonb" NOT NULL,
    "steps" "jsonb"[] NOT NULL,
    "enabled" boolean DEFAULT true,
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflows" OWNER TO "postgres";


ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."analytics_metrics"
    ADD CONSTRAINT "analytics_metrics_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."billing_codes"
    ADD CONSTRAINT "billing_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_conversation_id_user_id_key" UNIQUE ("conversation_id", "user_id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."departments"
    ADD CONSTRAINT "departments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."education_materials"
    ADD CONSTRAINT "education_materials_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."facilities"
    ADD CONSTRAINT "facilities_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."insurance_providers"
    ADD CONSTRAINT "insurance_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_message_id_user_id_key" UNIQUE ("message_id", "user_id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_user_id_type_key" UNIQUE ("user_id", "type");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_organization_id_name_key" UNIQUE ("organization_id", "name");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_portal_settings"
    ADD CONSTRAINT "patient_portal_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_questionnaires"
    ADD CONSTRAINT "patient_questionnaires_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_resource_key" UNIQUE ("role", "resource");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_task_id_user_id_key" UNIQUE ("task_id", "user_id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."templates"
    ADD CONSTRAINT "templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_organization_id_role_key" UNIQUE ("user_id", "organization_id", "role");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflow_instances"
    ADD CONSTRAINT "workflow_instances_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflow_logs"
    ADD CONSTRAINT "workflow_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_allergies_allergen" ON "public"."allergies" USING "btree" ("allergen");



CREATE INDEX "idx_allergies_patient_id" ON "public"."allergies" USING "btree" ("patient_id");



CREATE INDEX "idx_allergies_severity" ON "public"."allergies" USING "btree" ("severity");



CREATE INDEX "idx_allergies_status" ON "public"."allergies" USING "btree" ("status");



CREATE INDEX "idx_analytics_events_org_type" ON "public"."analytics_events" USING "btree" ("organization_id", "event_type");



CREATE INDEX "idx_analytics_events_timestamp" ON "public"."analytics_events" USING "brin" ("timestamp");



CREATE INDEX "idx_analytics_metrics_org_metric" ON "public"."analytics_metrics" USING "btree" ("organization_id", "metric_name");



CREATE INDEX "idx_analytics_metrics_timestamp" ON "public"."analytics_metrics" USING "brin" ("timestamp");



CREATE INDEX "idx_appointments_date" ON "public"."appointments" USING "btree" ("appointment_date");



CREATE INDEX "idx_appointments_patient_date" ON "public"."appointments" USING "btree" ("patient_id", "appointment_date");



CREATE INDEX "idx_appointments_patient_id" ON "public"."appointments" USING "btree" ("patient_id");



CREATE INDEX "idx_appointments_provider_date" ON "public"."appointments" USING "btree" ("provider_id", "appointment_date");



CREATE INDEX "idx_appointments_provider_id" ON "public"."appointments" USING "btree" ("provider_id");



CREATE INDEX "idx_appointments_provider_patient_date" ON "public"."appointments" USING "btree" ("provider_id", "patient_id", "appointment_date");



CREATE INDEX "idx_billing_codes_code" ON "public"."billing_codes" USING "btree" ("code");



CREATE INDEX "idx_billing_codes_type" ON "public"."billing_codes" USING "btree" ("type");



CREATE INDEX "idx_care_team_members_patient_id" ON "public"."care_team_members" USING "btree" ("patient_id");



CREATE INDEX "idx_care_team_members_primary_contact" ON "public"."care_team_members" USING "btree" ("primary_contact");



CREATE INDEX "idx_care_team_members_provider_id" ON "public"."care_team_members" USING "btree" ("provider_id");



CREATE INDEX "idx_care_team_members_role" ON "public"."care_team_members" USING "btree" ("role");



CREATE INDEX "idx_claims_insurance_provider_id" ON "public"."claims" USING "btree" ("insurance_provider_id");



CREATE INDEX "idx_claims_patient_id" ON "public"."claims" USING "btree" ("patient_id");



CREATE INDEX "idx_claims_provider_id" ON "public"."claims" USING "btree" ("provider_id");



CREATE INDEX "idx_claims_service_date" ON "public"."claims" USING "btree" ("service_date");



CREATE INDEX "idx_claims_status" ON "public"."claims" USING "btree" ("status");



CREATE INDEX "idx_clinical_notes_medical_record_id" ON "public"."clinical_notes" USING "btree" ("medical_record_id");



CREATE INDEX "idx_clinical_notes_signed_by" ON "public"."clinical_notes" USING "btree" ("signed_by");



CREATE UNIQUE INDEX "idx_daily_appointments_org_date_dept" ON "public"."analytics_daily_appointments" USING "btree" ("organization_id", "date", "department_id");



CREATE INDEX "idx_documents_created_by" ON "public"."documents" USING "btree" ("created_by");



CREATE INDEX "idx_documents_document_type" ON "public"."documents" USING "btree" ("document_type");



CREATE INDEX "idx_documents_organization_id" ON "public"."documents" USING "btree" ("organization_id");



CREATE INDEX "idx_documents_patient_id" ON "public"."documents" USING "btree" ("patient_id");



CREATE INDEX "idx_education_materials_category" ON "public"."education_materials" USING "btree" ("category");



CREATE INDEX "idx_education_materials_format" ON "public"."education_materials" USING "btree" ("format");



CREATE INDEX "idx_education_materials_language" ON "public"."education_materials" USING "btree" ("language");



CREATE INDEX "idx_immunizations_administered_by" ON "public"."immunizations" USING "btree" ("administered_by");



CREATE INDEX "idx_immunizations_administered_date" ON "public"."immunizations" USING "btree" ("administered_date");



CREATE INDEX "idx_immunizations_patient_id" ON "public"."immunizations" USING "btree" ("patient_id");



CREATE INDEX "idx_immunizations_vaccine_code" ON "public"."immunizations" USING "btree" ("vaccine_code");



CREATE INDEX "idx_immunizations_vaccine_name" ON "public"."immunizations" USING "btree" ("vaccine_name");



CREATE INDEX "idx_inventory_items_facility_id" ON "public"."inventory_items" USING "btree" ("facility_id");



CREATE INDEX "idx_inventory_items_organization_id" ON "public"."inventory_items" USING "btree" ("organization_id");



CREATE INDEX "idx_inventory_items_type" ON "public"."inventory_items" USING "btree" ("type");



CREATE INDEX "idx_inventory_transactions_item_id" ON "public"."inventory_transactions" USING "btree" ("item_id");



CREATE INDEX "idx_inventory_transactions_performed_by" ON "public"."inventory_transactions" USING "btree" ("performed_by");



CREATE INDEX "idx_lab_results_patient_date" ON "public"."lab_results" USING "btree" ("patient_id", "test_date");



CREATE INDEX "idx_lab_results_patient_id" ON "public"."lab_results" USING "btree" ("patient_id");



CREATE INDEX "idx_medical_records_patient_date" ON "public"."medical_records" USING "btree" ("patient_id", "visit_date");



CREATE INDEX "idx_medical_records_patient_id" ON "public"."medical_records" USING "btree" ("patient_id");



CREATE INDEX "idx_medical_records_provider_date" ON "public"."medical_records" USING "btree" ("provider_id", "visit_date");



CREATE INDEX "idx_medical_records_provider_patient_date" ON "public"."medical_records" USING "btree" ("provider_id", "patient_id", "visit_date");



CREATE INDEX "idx_medications_patient_active" ON "public"."medications" USING "btree" ("patient_id", "active");



CREATE INDEX "idx_medications_patient_id" ON "public"."medications" USING "btree" ("patient_id");



CREATE INDEX "idx_medications_provider_patient_active" ON "public"."medications" USING "btree" ("provider_id", "patient_id", "active");



CREATE INDEX "idx_orders_order_type" ON "public"."orders" USING "btree" ("order_type");



CREATE INDEX "idx_orders_ordered_at" ON "public"."orders" USING "btree" ("ordered_at");



CREATE INDEX "idx_orders_ordering_provider_id" ON "public"."orders" USING "btree" ("ordering_provider_id");



CREATE INDEX "idx_orders_patient_id" ON "public"."orders" USING "btree" ("patient_id");



CREATE INDEX "idx_orders_priority" ON "public"."orders" USING "btree" ("priority");



CREATE INDEX "idx_orders_scheduled_date" ON "public"."orders" USING "btree" ("scheduled_date");



CREATE INDEX "idx_orders_status" ON "public"."orders" USING "btree" ("status");



CREATE INDEX "idx_patient_alerts_alert_type" ON "public"."patient_alerts" USING "btree" ("alert_type");



CREATE INDEX "idx_patient_alerts_patient_id" ON "public"."patient_alerts" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_alerts_severity" ON "public"."patient_alerts" USING "btree" ("severity");



CREATE INDEX "idx_patient_alerts_status" ON "public"."patient_alerts" USING "btree" ("status");



CREATE INDEX "idx_patient_education_records_material_id" ON "public"."patient_education_records" USING "btree" ("material_id");



CREATE INDEX "idx_patient_education_records_patient_id" ON "public"."patient_education_records" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_education_records_provided_date" ON "public"."patient_education_records" USING "btree" ("provided_date");



CREATE INDEX "idx_patient_education_records_provider_id" ON "public"."patient_education_records" USING "btree" ("provider_id");



CREATE UNIQUE INDEX "idx_patient_metrics_org" ON "public"."analytics_patient_metrics" USING "btree" ("organization_id");



CREATE INDEX "idx_patient_portal_settings_patient_id" ON "public"."patient_portal_settings" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_questionnaires_patient_id" ON "public"."patient_questionnaires" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_questionnaires_type" ON "public"."patient_questionnaires" USING "btree" ("questionnaire_type");



CREATE UNIQUE INDEX "idx_provider_metrics_org_provider" ON "public"."analytics_provider_metrics" USING "btree" ("organization_id", "provider_id");



CREATE INDEX "idx_referrals_patient_id" ON "public"."referrals" USING "btree" ("patient_id");



CREATE INDEX "idx_referrals_priority" ON "public"."referrals" USING "btree" ("priority");



CREATE INDEX "idx_referrals_referred_to_provider_id" ON "public"."referrals" USING "btree" ("referred_to_provider_id");



CREATE INDEX "idx_referrals_referring_provider_id" ON "public"."referrals" USING "btree" ("referring_provider_id");



CREATE INDEX "idx_referrals_status" ON "public"."referrals" USING "btree" ("status");



CREATE INDEX "idx_templates_organization_id" ON "public"."templates" USING "btree" ("organization_id");



CREATE INDEX "idx_templates_type" ON "public"."templates" USING "btree" ("type");



CREATE INDEX "idx_vital_signs_patient_id" ON "public"."vital_signs" USING "btree" ("patient_id");



CREATE INDEX "idx_vital_signs_recorded_at" ON "public"."vital_signs" USING "btree" ("recorded_at");



CREATE INDEX "idx_vital_signs_recorded_by" ON "public"."vital_signs" USING "btree" ("recorded_by");



CREATE INDEX "medical_records_search_idx" ON "public"."medical_records" USING "gin" ("search_vector");



CREATE INDEX "patients_search_idx" ON "public"."patients" USING "gin" ("search_vector");



CREATE OR REPLACE TRIGGER "audit_appointments" AFTER INSERT OR DELETE OR UPDATE ON "public"."appointments" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "audit_healthcare_providers" AFTER INSERT OR DELETE OR UPDATE ON "public"."healthcare_providers" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "audit_lab_results" AFTER INSERT OR DELETE OR UPDATE ON "public"."lab_results" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "audit_medical_records" AFTER INSERT OR DELETE OR UPDATE ON "public"."medical_records" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "audit_medications" AFTER INSERT OR DELETE OR UPDATE ON "public"."medications" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "audit_patients" AFTER INSERT OR DELETE OR UPDATE ON "public"."patients" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "medical_record_search_vector_update" BEFORE INSERT OR UPDATE ON "public"."medical_records" FOR EACH ROW EXECUTE FUNCTION "public"."update_medical_record_search_vector"();



CREATE OR REPLACE TRIGGER "patient_search_vector_update" BEFORE INSERT OR UPDATE ON "public"."patients" FOR EACH ROW EXECUTE FUNCTION "public"."update_patient_search_vector"();



ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_reported_by_fkey" FOREIGN KEY ("reported_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."analytics_metrics"
    ADD CONSTRAINT "analytics_metrics_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_insurance_provider_id_fkey" FOREIGN KEY ("insurance_provider_id") REFERENCES "public"."insurance_providers"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_medical_record_id_fkey" FOREIGN KEY ("medical_record_id") REFERENCES "public"."medical_records"("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_signed_by_fkey" FOREIGN KEY ("signed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."departments"
    ADD CONSTRAINT "departments_facility_id_fkey" FOREIGN KEY ("facility_id") REFERENCES "public"."facilities"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."facilities"
    ADD CONSTRAINT "facilities_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_administered_by_fkey" FOREIGN KEY ("administered_by") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_facility_id_fkey" FOREIGN KEY ("facility_id") REFERENCES "public"."facilities"("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_item_id_fkey" FOREIGN KEY ("item_id") REFERENCES "public"."inventory_items"("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."messages"("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_recipient_id_fkey" FOREIGN KEY ("recipient_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_ordering_provider_id_fkey" FOREIGN KEY ("ordering_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_material_id_fkey" FOREIGN KEY ("material_id") REFERENCES "public"."education_materials"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."patient_portal_settings"
    ADD CONSTRAINT "patient_portal_settings_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_questionnaires"
    ADD CONSTRAINT "patient_questionnaires_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_referred_to_provider_id_fkey" FOREIGN KEY ("referred_to_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_referring_provider_id_fkey" FOREIGN KEY ("referring_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_leader_id_fkey" FOREIGN KEY ("leader_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."templates"
    ADD CONSTRAINT "templates_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_recorded_by_fkey" FOREIGN KEY ("recorded_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."workflow_instances"
    ADD CONSTRAINT "workflow_instances_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "public"."workflows"("id");



ALTER TABLE ONLY "public"."workflow_logs"
    ADD CONSTRAINT "workflow_logs_workflow_instance_id_fkey" FOREIGN KEY ("workflow_instance_id") REFERENCES "public"."workflow_instances"("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



CREATE POLICY "Allow read access to audit logs for healthcare providers" ON "public"."audit_logs" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."id" = "auth"."uid"()))));



CREATE POLICY "Create appointments" ON "public"."appointments" FOR INSERT TO "authenticated" WITH CHECK ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



CREATE POLICY "Create lab results" ON "public"."lab_results" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Create medical records" ON "public"."medical_records" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Create medications" ON "public"."medications" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Organization admins can manage notification templates" ON "public"."notification_templates" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "notification_templates"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role"]))))));



CREATE POLICY "Organization admins can manage workflows" ON "public"."workflows" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "workflows"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role"]))))));



CREATE POLICY "Patients can update their own profile" ON "public"."patients" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id")) WITH CHECK (("auth"."uid"() = "id"));



CREATE POLICY "Patients can view own profile" ON "public"."patients" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Patients can view their appointments" ON "public"."appointments" FOR SELECT USING (("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = "auth"."uid"()))));



CREATE POLICY "Patients can view their lab results" ON "public"."lab_results" FOR SELECT USING (("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = "auth"."uid"()))));



CREATE POLICY "Patients can view their medications" ON "public"."medications" FOR SELECT USING (("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = "auth"."uid"()))));



CREATE POLICY "Patients can view their own profile" ON "public"."patients" FOR SELECT TO "authenticated" USING ((("auth"."uid"() = "id") OR ("public"."is_healthcare_provider"("auth"."uid"()) AND "public"."has_patient_access"("auth"."uid"(), "id"))));



CREATE POLICY "Patients can view their records" ON "public"."medical_records" FOR SELECT USING (("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = "auth"."uid"()))));



CREATE POLICY "Providers can update their own profile" ON "public"."healthcare_providers" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id")) WITH CHECK (("auth"."uid"() = "id"));



CREATE POLICY "Providers can view assigned patient lab results" ON "public"."lab_results" FOR SELECT USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = "auth"."uid"()))) OR ("patient_id" IN ( SELECT "p"."id"
   FROM ("public"."patients" "p"
     JOIN "public"."appointments" "a" ON (("a"."patient_id" = "p"."id")))
  WHERE ("a"."provider_id" IN ( SELECT "healthcare_providers"."id"
           FROM "public"."healthcare_providers"
          WHERE ("healthcare_providers"."user_id" = "auth"."uid"())))))));



CREATE POLICY "Providers can view assigned patient medications" ON "public"."medications" FOR SELECT USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = "auth"."uid"()))) OR ("patient_id" IN ( SELECT "p"."id"
   FROM ("public"."patients" "p"
     JOIN "public"."appointments" "a" ON (("a"."patient_id" = "p"."id")))
  WHERE ("a"."provider_id" IN ( SELECT "healthcare_providers"."id"
           FROM "public"."healthcare_providers"
          WHERE ("healthcare_providers"."user_id" = "auth"."uid"())))))));



CREATE POLICY "Providers can view assigned patient records" ON "public"."medical_records" FOR SELECT USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = "auth"."uid"()))) OR ("patient_id" IN ( SELECT "p"."id"
   FROM ("public"."patients" "p"
     JOIN "public"."appointments" "a" ON (("a"."patient_id" = "p"."id")))
  WHERE ("a"."provider_id" IN ( SELECT "healthcare_providers"."id"
           FROM "public"."healthcare_providers"
          WHERE ("healthcare_providers"."user_id" = "auth"."uid"())))))));



CREATE POLICY "Providers can view assigned patients" ON "public"."patients" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."appointments"
  WHERE (("appointments"."patient_id" = "patients"."id") AND ("appointments"."provider_id" IN ( SELECT "healthcare_providers"."id"
           FROM "public"."healthcare_providers"
          WHERE ("healthcare_providers"."user_id" = "auth"."uid"())))))));



CREATE POLICY "Providers can view own profile" ON "public"."healthcare_providers" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Providers can view their appointments" ON "public"."appointments" FOR SELECT USING (("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = "auth"."uid"()))));



CREATE POLICY "Providers can view their own profile" ON "public"."healthcare_providers" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "Update appointments" ON "public"."appointments" FOR UPDATE TO "authenticated" USING ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"()))) WITH CHECK ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



CREATE POLICY "Update lab results" ON "public"."lab_results" FOR UPDATE TO "authenticated" USING ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"()))) WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Update medical records" ON "public"."medical_records" FOR UPDATE TO "authenticated" USING ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"()))) WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Update medications" ON "public"."medications" FOR UPDATE TO "authenticated" USING ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"()))) WITH CHECK ((("provider_id" = "auth"."uid"()) AND "public"."is_healthcare_provider"("auth"."uid"())));



CREATE POLICY "Users can add comments to tasks they have access to" ON "public"."task_comments" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "task_comments"."task_id") AND (("tasks"."assigned_to" = "auth"."uid"()) OR ("tasks"."assigned_by" = "auth"."uid"()) OR (EXISTS ( SELECT 1
           FROM "public"."task_watchers"
          WHERE (("task_watchers"."task_id" = "tasks"."id") AND ("task_watchers"."user_id" = "auth"."uid"())))))))));



CREATE POLICY "Users can manage their notification preferences" ON "public"."notification_preferences" TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can send messages to their conversations" ON "public"."messages" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "messages"."conversation_id") AND ("conversation_participants"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update tasks they're assigned to" ON "public"."tasks" FOR UPDATE TO "authenticated" USING (("assigned_to" = "auth"."uid"())) WITH CHECK (("assigned_to" = "auth"."uid"()));



CREATE POLICY "Users can update their message states" ON "public"."message_states" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can update their own notifications" ON "public"."notifications" FOR UPDATE TO "authenticated" USING (("recipient_id" = "auth"."uid"())) WITH CHECK (("recipient_id" = "auth"."uid"()));



CREATE POLICY "Users can view analytics for their organization" ON "public"."analytics_metrics" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "analytics_metrics"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"]))))));



CREATE POLICY "Users can view conversations they're part of" ON "public"."conversations" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "conversations"."id") AND ("conversation_participants"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view events for their organization" ON "public"."analytics_events" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "analytics_events"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"]))))));



CREATE POLICY "Users can view messages in their conversations" ON "public"."messages" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "messages"."conversation_id") AND ("conversation_participants"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view task comments they have access to" ON "public"."task_comments" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "task_comments"."task_id") AND (("tasks"."assigned_to" = "auth"."uid"()) OR ("tasks"."assigned_by" = "auth"."uid"()) OR (EXISTS ( SELECT 1
           FROM "public"."task_watchers"
          WHERE (("task_watchers"."task_id" = "tasks"."id") AND ("task_watchers"."user_id" = "auth"."uid"())))))))));



CREATE POLICY "Users can view tasks they're involved with" ON "public"."tasks" FOR SELECT TO "authenticated" USING ((("assigned_to" = "auth"."uid"()) OR ("assigned_by" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."task_watchers"
  WHERE (("task_watchers"."task_id" = "tasks"."id") AND ("task_watchers"."user_id" = "auth"."uid"()))))));



CREATE POLICY "Users can view their message states" ON "public"."message_states" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view their own notifications" ON "public"."notifications" FOR SELECT TO "authenticated" USING (("recipient_id" = "auth"."uid"()));



CREATE POLICY "Users can view workflow instances for their organization" ON "public"."workflow_instances" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."workflows" "w"
     JOIN "public"."user_roles" "ur" ON (("ur"."organization_id" = "w"."organization_id")))
  WHERE (("w"."id" = "workflow_instances"."workflow_id") AND ("ur"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view workflow logs for their organization" ON "public"."workflow_logs" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM (("public"."workflow_instances" "wi"
     JOIN "public"."workflows" "w" ON (("w"."id" = "wi"."workflow_id")))
     JOIN "public"."user_roles" "ur" ON (("ur"."organization_id" = "w"."organization_id")))
  WHERE (("wi"."id" = "workflow_logs"."workflow_instance_id") AND ("ur"."user_id" = "auth"."uid"())))));



CREATE POLICY "View appointments" ON "public"."appointments" FOR SELECT TO "authenticated" USING ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



CREATE POLICY "View lab results" ON "public"."lab_results" FOR SELECT TO "authenticated" USING ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



CREATE POLICY "View medical records" ON "public"."medical_records" FOR SELECT TO "authenticated" USING ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



CREATE POLICY "View medications" ON "public"."medications" FOR SELECT TO "authenticated" USING ((("patient_id" = "auth"."uid"()) OR ("provider_id" = "auth"."uid"())));



ALTER TABLE "public"."allergies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."analytics_events" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."analytics_metrics" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."appointments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."audit_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."billing_codes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."care_team_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."claims" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."clinical_notes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."conversation_participants" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."conversations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."departments" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "departments_access" ON "public"."departments" USING ("public"."check_user_permission"("auth"."uid"(), 'departments'::"text", 'read'::"text", "jsonb_build_object"('org_id', ( SELECT "facilities"."organization_id"
   FROM "public"."facilities"
  WHERE ("facilities"."id" = "departments"."facility_id"))))) WITH CHECK ("public"."check_user_permission"("auth"."uid"(), 'departments'::"text", 'update'::"text", "jsonb_build_object"('org_id', ( SELECT "facilities"."organization_id"
   FROM "public"."facilities"
  WHERE ("facilities"."id" = "departments"."facility_id")))));



ALTER TABLE "public"."documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."education_materials" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."facilities" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "facilities_access" ON "public"."facilities" USING ("public"."check_user_permission"("auth"."uid"(), 'facilities'::"text", 'read'::"text", "jsonb_build_object"('org_id', "organization_id"))) WITH CHECK ("public"."check_user_permission"("auth"."uid"(), 'facilities'::"text", 'update'::"text", "jsonb_build_object"('org_id', "organization_id")));



ALTER TABLE "public"."healthcare_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."immunizations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."insurance_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."inventory_items" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."inventory_transactions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."lab_results" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."medical_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."medications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."message_states" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notification_preferences" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notification_templates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notifications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."orders" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "organizations_policy" ON "public"."organizations" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."patient_alerts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_education_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_portal_settings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_questionnaires" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patients" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."referrals" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."role_permissions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."task_comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."task_watchers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tasks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."templates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_roles" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_roles_policy" ON "public"."user_roles" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."vital_signs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflow_instances" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflow_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflows" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "service_role";



GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."disable_rls"() TO "anon";
GRANT ALL ON FUNCTION "public"."disable_rls"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."disable_rls"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."random_timestamp"("start_date" timestamp without time zone, "end_date" timestamp without time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."random_timestamp"("start_date" timestamp without time zone, "end_date" timestamp without time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."random_timestamp"("start_date" timestamp without time zone, "end_date" timestamp without time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "anon";
GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "service_role";



GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "postgres";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "anon";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "service_role";



GRANT ALL ON FUNCTION "public"."show_limit"() TO "postgres";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "anon";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "service_role";



GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "postgres";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "anon";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "service_role";



























GRANT ALL ON TABLE "public"."allergies" TO "anon";
GRANT ALL ON TABLE "public"."allergies" TO "authenticated";
GRANT ALL ON TABLE "public"."allergies" TO "service_role";



GRANT ALL ON TABLE "public"."appointments" TO "anon";
GRANT ALL ON TABLE "public"."appointments" TO "authenticated";
GRANT ALL ON TABLE "public"."appointments" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "anon";
GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_events" TO "anon";
GRANT ALL ON TABLE "public"."analytics_events" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_events" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."medical_records" TO "anon";
GRANT ALL ON TABLE "public"."medical_records" TO "authenticated";
GRANT ALL ON TABLE "public"."medical_records" TO "service_role";



GRANT ALL ON TABLE "public"."medications" TO "anon";
GRANT ALL ON TABLE "public"."medications" TO "authenticated";
GRANT ALL ON TABLE "public"."medications" TO "service_role";



GRANT ALL ON TABLE "public"."patients" TO "anon";
GRANT ALL ON TABLE "public"."patients" TO "authenticated";
GRANT ALL ON TABLE "public"."patients" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."healthcare_providers" TO "anon";
GRANT ALL ON TABLE "public"."healthcare_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."healthcare_providers" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."audit_logs" TO "anon";
GRANT ALL ON TABLE "public"."audit_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."audit_logs" TO "service_role";



GRANT ALL ON TABLE "public"."billing_codes" TO "anon";
GRANT ALL ON TABLE "public"."billing_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."billing_codes" TO "service_role";



GRANT ALL ON TABLE "public"."care_team_members" TO "anon";
GRANT ALL ON TABLE "public"."care_team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."care_team_members" TO "service_role";



GRANT ALL ON TABLE "public"."claims" TO "anon";
GRANT ALL ON TABLE "public"."claims" TO "authenticated";
GRANT ALL ON TABLE "public"."claims" TO "service_role";



GRANT ALL ON TABLE "public"."clinical_notes" TO "anon";
GRANT ALL ON TABLE "public"."clinical_notes" TO "authenticated";
GRANT ALL ON TABLE "public"."clinical_notes" TO "service_role";



GRANT ALL ON TABLE "public"."conversation_participants" TO "anon";
GRANT ALL ON TABLE "public"."conversation_participants" TO "authenticated";
GRANT ALL ON TABLE "public"."conversation_participants" TO "service_role";



GRANT ALL ON TABLE "public"."conversations" TO "anon";
GRANT ALL ON TABLE "public"."conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."conversations" TO "service_role";



GRANT ALL ON TABLE "public"."departments" TO "anon";
GRANT ALL ON TABLE "public"."departments" TO "authenticated";
GRANT ALL ON TABLE "public"."departments" TO "service_role";



GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";



GRANT ALL ON TABLE "public"."education_materials" TO "anon";
GRANT ALL ON TABLE "public"."education_materials" TO "authenticated";
GRANT ALL ON TABLE "public"."education_materials" TO "service_role";



GRANT ALL ON TABLE "public"."facilities" TO "anon";
GRANT ALL ON TABLE "public"."facilities" TO "authenticated";
GRANT ALL ON TABLE "public"."facilities" TO "service_role";



GRANT ALL ON TABLE "public"."immunizations" TO "anon";
GRANT ALL ON TABLE "public"."immunizations" TO "authenticated";
GRANT ALL ON TABLE "public"."immunizations" TO "service_role";



GRANT ALL ON TABLE "public"."insurance_providers" TO "anon";
GRANT ALL ON TABLE "public"."insurance_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."insurance_providers" TO "service_role";



GRANT ALL ON TABLE "public"."inventory_items" TO "anon";
GRANT ALL ON TABLE "public"."inventory_items" TO "authenticated";
GRANT ALL ON TABLE "public"."inventory_items" TO "service_role";



GRANT ALL ON TABLE "public"."inventory_transactions" TO "anon";
GRANT ALL ON TABLE "public"."inventory_transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."inventory_transactions" TO "service_role";



GRANT ALL ON TABLE "public"."lab_results" TO "anon";
GRANT ALL ON TABLE "public"."lab_results" TO "authenticated";
GRANT ALL ON TABLE "public"."lab_results" TO "service_role";



GRANT ALL ON TABLE "public"."message_states" TO "anon";
GRANT ALL ON TABLE "public"."message_states" TO "authenticated";
GRANT ALL ON TABLE "public"."message_states" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON TABLE "public"."notification_preferences" TO "anon";
GRANT ALL ON TABLE "public"."notification_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_preferences" TO "service_role";



GRANT ALL ON TABLE "public"."notification_templates" TO "anon";
GRANT ALL ON TABLE "public"."notification_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_templates" TO "service_role";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";



GRANT ALL ON TABLE "public"."orders" TO "anon";
GRANT ALL ON TABLE "public"."orders" TO "authenticated";
GRANT ALL ON TABLE "public"."orders" TO "service_role";



GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";



GRANT ALL ON TABLE "public"."patient_alerts" TO "anon";
GRANT ALL ON TABLE "public"."patient_alerts" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_alerts" TO "service_role";



GRANT ALL ON TABLE "public"."patient_education_records" TO "anon";
GRANT ALL ON TABLE "public"."patient_education_records" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_education_records" TO "service_role";



GRANT ALL ON TABLE "public"."patient_portal_settings" TO "anon";
GRANT ALL ON TABLE "public"."patient_portal_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_portal_settings" TO "service_role";



GRANT ALL ON TABLE "public"."patient_questionnaires" TO "anon";
GRANT ALL ON TABLE "public"."patient_questionnaires" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_questionnaires" TO "service_role";



GRANT ALL ON TABLE "public"."referrals" TO "anon";
GRANT ALL ON TABLE "public"."referrals" TO "authenticated";
GRANT ALL ON TABLE "public"."referrals" TO "service_role";



GRANT ALL ON TABLE "public"."role_permissions" TO "anon";
GRANT ALL ON TABLE "public"."role_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."role_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."task_comments" TO "anon";
GRANT ALL ON TABLE "public"."task_comments" TO "authenticated";
GRANT ALL ON TABLE "public"."task_comments" TO "service_role";



GRANT ALL ON TABLE "public"."task_watchers" TO "anon";
GRANT ALL ON TABLE "public"."task_watchers" TO "authenticated";
GRANT ALL ON TABLE "public"."task_watchers" TO "service_role";



GRANT ALL ON TABLE "public"."tasks" TO "anon";
GRANT ALL ON TABLE "public"."tasks" TO "authenticated";
GRANT ALL ON TABLE "public"."tasks" TO "service_role";



GRANT ALL ON TABLE "public"."teams" TO "anon";
GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT ALL ON TABLE "public"."teams" TO "service_role";



GRANT ALL ON TABLE "public"."templates" TO "anon";
GRANT ALL ON TABLE "public"."templates" TO "authenticated";
GRANT ALL ON TABLE "public"."templates" TO "service_role";



GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";



GRANT ALL ON TABLE "public"."vital_signs" TO "anon";
GRANT ALL ON TABLE "public"."vital_signs" TO "authenticated";
GRANT ALL ON TABLE "public"."vital_signs" TO "service_role";



GRANT ALL ON TABLE "public"."workflow_instances" TO "anon";
GRANT ALL ON TABLE "public"."workflow_instances" TO "authenticated";
GRANT ALL ON TABLE "public"."workflow_instances" TO "service_role";



GRANT ALL ON TABLE "public"."workflow_logs" TO "anon";
GRANT ALL ON TABLE "public"."workflow_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."workflow_logs" TO "service_role";



GRANT ALL ON TABLE "public"."workflows" TO "anon";
GRANT ALL ON TABLE "public"."workflows" TO "authenticated";
GRANT ALL ON TABLE "public"."workflows" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
