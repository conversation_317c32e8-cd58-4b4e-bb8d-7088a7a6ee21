-- Start transaction
BEGIN;

-- Insert Notification Templates
WITH org_data AS (
  SELECT id, name
  FROM public.organizations
),
template_nums AS (
  SELECT 1 as template_num UNION SELECT 2 UNION SELECT 3
)
INSERT INTO public.notification_templates (
  id,
  organization_id,
  name,
  type,
  subject_template,
  content_template,
  metadata_template,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'Appointment Reminder'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'Lab Results Available'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'Prescription Refill'
    WHEN o.name = 'Spritely Community Clinic' AND t.template_num = 1 THEN 'Appointment Confirmation'
    WHEN o.name = 'Spritely Pediatrics' AND t.template_num = 1 THEN 'Vaccination Due'
  END as name,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'appointment_reminder'::notification_type
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'lab_result'::notification_type
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'prescription_update'::notification_type
    WHEN o.name = 'Spritely Community Clinic' AND t.template_num = 1 THEN 'appointment_reminder'::notification_type
    WHEN o.name = 'Spritely Pediatrics' AND t.template_num = 1 THEN 'medical_record_update'::notification_type
  END as type,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'Reminder: Your appointment is tomorrow'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'Your Lab Results Are Available'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'Prescription Refill Ready'
    WHEN o.name = 'Spritely Community Clinic' AND t.template_num = 1 THEN 'Your Appointment Has Been Scheduled'
    WHEN o.name = 'Spritely Pediatrics' AND t.template_num = 1 THEN 'Vaccination Reminder'
  END as subject_template,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'Dear {{patient_name}}, This is a reminder that you have an appointment with {{provider_name}} tomorrow at {{appointment_time}}. Please arrive 15 minutes early to complete any necessary paperwork.'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'Dear {{patient_name}}, Your recent lab results are now available. Please log in to your patient portal to view them or contact our office if you have any questions.'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'Your prescription for {{medication_name}} is ready for pickup at your pharmacy.'
    WHEN o.name = 'Spritely Community Clinic' AND t.template_num = 1 THEN 'Dear {{patient_name}}, Your appointment with {{provider_name}} has been scheduled for {{appointment_date}} at {{appointment_time}}.'
    WHEN o.name = 'Spritely Pediatrics' AND t.template_num = 1 THEN 'Dear {{parent_name}}, This is a reminder that {{patient_name}} is due for the following vaccinations: {{vaccine_list}}. Please call our office to schedule an appointment.'
  END as content_template,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN '{"variables": ["patient_name", "provider_name", "appointment_time"], "delivery_method": "email"}'::jsonb
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN '{"variables": ["patient_name"], "delivery_method": "email"}'::jsonb
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN '{"variables": ["medication_name"], "delivery_method": "sms"}'::jsonb
    WHEN o.name = 'Spritely Community Clinic' AND t.template_num = 1 THEN '{"variables": ["patient_name", "provider_name", "appointment_date", "appointment_time"], "delivery_method": "email"}'::jsonb
    WHEN o.name = 'Spritely Pediatrics' AND t.template_num = 1 THEN '{"variables": ["parent_name", "patient_name", "vaccine_list"], "delivery_method": "email"}'::jsonb
  END as metadata_template,
  NOW(),
  NOW()
FROM org_data o
CROSS JOIN template_nums t
WHERE (o.name = 'Spritely Medical Center') OR
      (o.name = 'Spritely Community Clinic' AND t.template_num = 1) OR
      (o.name = 'Spritely Pediatrics' AND t.template_num = 1);

COMMIT;
