-- Comprehensive Medical Billing Codes - CPT, ICD-10, HCPCS
BEGIN;

-- Insert CPT Codes (Current Procedural Terminology)
INSERT INTO public.billing_codes (
  id,
  code,
  description,
  type,
  effective_date,
  created_at,
  updated_at
)
VALUES
  -- Evaluation & Management Codes
  (uuid_generate_v4(), '99201', 'Office visit, new patient, straightforward', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99202', 'Office visit, new patient, low complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99203', 'Office visit, new patient, moderate complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99204', 'Office visit, new patient, moderate to high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99205', 'Office visit, new patient, high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99211', 'Office visit, established patient, minimal', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99212', 'Office visit, established patient, straightforward', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99213', 'Office visit, established patient, low complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99214', 'Office visit, established patient, moderate complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99215', 'Office visit, established patient, high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Emergency Department Codes
  (uuid_generate_v4(), '99281', 'Emergency department visit, straightforward', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99282', 'Emergency department visit, low complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99283', 'Emergency department visit, moderate complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99284', 'Emergency department visit, moderate to high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99285', 'Emergency department visit, high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Laboratory Codes
  (uuid_generate_v4(), '80053', 'Comprehensive metabolic panel', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '80061', 'Lipid panel', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '85025', 'Complete blood count (CBC) with differential', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '85027', 'Complete blood count (CBC)', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '83036', 'Hemoglobin A1C', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '84443', 'Thyroid stimulating hormone (TSH)', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '87086', 'Urine culture', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '87880', 'Streptococcus, group A', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Radiology Codes
  (uuid_generate_v4(), '71045', 'Chest X-ray, single view', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '71046', 'Chest X-ray, two views', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '70450', 'CT head/brain without contrast', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '70460', 'CT head/brain with contrast', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '72148', 'MRI lumbar spine without contrast', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '76700', 'Ultrasound, abdominal, complete', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '93306', 'Echocardiogram, complete', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '93000', 'Electrocardiogram (ECG)', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Cardiology Codes
  (uuid_generate_v4(), '93015', 'Cardiovascular stress test', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '93224', 'Holter monitor, 24 hours', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '93458', 'Cardiac catheterization, left heart', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Surgical Codes
  (uuid_generate_v4(), '44970', 'Laparoscopic appendectomy', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '47562', 'Laparoscopic cholecystectomy', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '29881', 'Arthroscopy, knee, surgical', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '66984', 'Cataract surgery with IOL', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Preventive Care Codes
  (uuid_generate_v4(), '99381', 'Initial preventive physical exam, infant', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99391', 'Periodic preventive physical exam, infant', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99395', 'Periodic preventive physical exam, 18-39 years', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99396', 'Periodic preventive physical exam, 40-64 years', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99397', 'Periodic preventive physical exam, 65+ years', 'cpt', CURRENT_DATE, NOW(), NOW()),

  -- Immunization Codes
  (uuid_generate_v4(), '90471', 'Immunization administration, first injection', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '90472', 'Immunization administration, additional injection', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '90630', 'Influenza vaccine, quadrivalent', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '90715', 'Tetanus, diphtheria, pertussis vaccine', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '90736', 'Zoster (shingles) vaccine', 'cpt', CURRENT_DATE, NOW(), NOW()),

-- ICD-10 Diagnosis Codes
  -- Common Primary Care Diagnoses
  (uuid_generate_v4(), 'I10', 'Essential hypertension', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'E11.9', 'Type 2 diabetes mellitus without complications', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'E78.5', 'Hyperlipidemia, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'J45.9', 'Asthma, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'M79.3', 'Panniculitis, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'Z00.00', 'Encounter for general adult medical examination without abnormal findings', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'Z23', 'Encounter for immunization', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Cardiovascular Conditions
  (uuid_generate_v4(), 'I25.10', 'Atherosclerotic heart disease of native coronary artery without angina pectoris', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'I48.91', 'Unspecified atrial fibrillation', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'I50.9', 'Heart failure, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Respiratory Conditions
  (uuid_generate_v4(), 'J44.1', 'Chronic obstructive pulmonary disease with acute exacerbation', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'J06.9', 'Acute upper respiratory infection, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'J18.9', 'Pneumonia, unspecified organism', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Musculoskeletal Conditions
  (uuid_generate_v4(), 'M54.5', 'Low back pain', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'M25.511', 'Pain in right shoulder', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'M17.11', 'Unilateral primary osteoarthritis, right knee', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Mental Health Conditions
  (uuid_generate_v4(), 'F32.9', 'Major depressive disorder, single episode, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'F41.9', 'Anxiety disorder, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'F90.9', 'Attention-deficit hyperactivity disorder, unspecified type', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Pediatric Conditions
  (uuid_generate_v4(), 'Z00.121', 'Encounter for routine child health examination with abnormal findings', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'J02.9', 'Acute pharyngitis, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'H66.90', 'Otitis media, unspecified, unspecified ear', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Emergency/Acute Conditions
  (uuid_generate_v4(), 'R50.9', 'Fever, unspecified', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'R06.02', 'Shortness of breath', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'R10.9', 'Unspecified abdominal pain', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'S72.001A', 'Fracture of unspecified part of neck of right femur, initial encounter', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Women''s Health
  (uuid_generate_v4(), 'Z01.419', 'Encounter for gynecological examination (general) (routine) without abnormal findings', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'O80', 'Encounter for full-term uncomplicated delivery', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'N92.0', 'Excessive and frequent menstruation with regular cycle', 'icd10', CURRENT_DATE, NOW(), NOW()),

  -- Chronic Conditions
  (uuid_generate_v4(), 'N18.6', 'End stage renal disease', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'C78.00', 'Secondary malignant neoplasm of unspecified lung', 'icd10', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), 'G93.1', 'Anoxic brain damage, not elsewhere classified', 'icd10', CURRENT_DATE, NOW(), NOW());

COMMIT;
