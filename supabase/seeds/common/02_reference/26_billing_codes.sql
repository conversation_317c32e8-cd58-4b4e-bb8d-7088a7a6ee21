-- Start transaction
BEGIN;

-- Insert Billing Codes
INSERT INTO public.billing_codes (
  id,
  code,
  description,
  type,
  effective_date,
  created_at,
  updated_at
)
VALUES
  (uuid_generate_v4(), '99213', 'Office visit, established patient, low complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99214', 'Office visit, established patient, moderate complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '99215', 'Office visit, established patient, high complexity', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '85025', 'Complete blood count (CBC)', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '93306', 'Echocardiogram, complete', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '87880', 'Strep test', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '71045', 'Chest X-ray, single view', 'cpt', CURRENT_DATE, NOW(), NOW()),
  (uuid_generate_v4(), '95004', 'Allergy test', 'cpt', CURRENT_DATE, NOW(), NOW());

COMMIT;
