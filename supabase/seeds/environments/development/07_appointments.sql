-- Start transaction
BEGIN;

-- Insert Appointments
DO $$
DECLARE
  patient_rec RECORD;
  provider_rec RECORD;
  department_rec RECORD;
  appointment_date TIMESTAMP;
  reason_text TEXT;
  status_val appointment_status;
  duration_val INT;
  notes_text TEXT;
BEGIN
  -- Create appointments for each organization
  FOR patient_rec IN
    SELECT p.id, p.first_name || ' ' || p.last_name AS name, p.organization_id
    FROM public.patients p
  LOOP
    -- Find a provider in the same organization
    SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name
    INTO provider_rec
    FROM public.healthcare_providers hp
    WHERE hp.organization_id = patient_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Find a department in the same organization
    SELECT d.id
    INTO department_rec
    FROM public.departments d
    JOIN public.facilities f ON d.facility_id = f.id
    WHERE f.organization_id = patient_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Skip if we couldn't find a provider or department
    CONTINUE WHEN provider_rec IS NULL OR department_rec IS NULL;

    -- Create 1-3 appointments for each patient
    FOR i IN 1..floor(random() * 3) + 1 LOOP
      -- Generate a random appointment date
      appointment_date := NOW() + (random() * 60 - 30) * INTERVAL '1 day';

      -- Set reason based on random selection
      CASE floor(random() * 10)::integer
        WHEN 0 THEN reason_text := 'Annual physical';
        WHEN 1 THEN reason_text := 'Follow-up visit';
        WHEN 2 THEN reason_text := 'Sick visit';
        WHEN 3 THEN reason_text := 'Vaccination';
        WHEN 4 THEN reason_text := 'Consultation';
        WHEN 5 THEN reason_text := 'Preventive care';
        WHEN 6 THEN reason_text := 'Chronic disease management';
        WHEN 7 THEN reason_text := 'Prescription refill';
        WHEN 8 THEN reason_text := 'Lab results review';
        ELSE reason_text := 'Specialist referral';
      END CASE;

      -- Set status based on appointment date
      IF appointment_date < NOW() THEN
        IF random() < 0.7 THEN
          status_val := 'completed';
        ELSIF random() < 0.85 THEN
          status_val := 'cancelled';
        ELSE
          status_val := 'no_show';
        END IF;
      ELSIF appointment_date < NOW() + INTERVAL '1 hour' THEN
        IF random() < 0.5 THEN
          status_val := 'checked_in';
        ELSIF random() < 0.8 THEN
          status_val := 'in_progress';
        ELSE
          status_val := 'scheduled';
        END IF;
      ELSE
        status_val := 'scheduled';
      END IF;

      -- Set duration based on reason
      IF reason_text LIKE '%physical%' THEN
        duration_val := 45;
      ELSIF reason_text LIKE '%consultation%' THEN
        duration_val := 60;
      ELSE
        duration_val := 30;
      END IF;

      -- Set notes
      notes_text := 'Appointment for ' || patient_rec.name || ' with ' || provider_rec.name || ' for ' || reason_text;

      -- Insert the appointment
      INSERT INTO public.appointments (
        id,
        organization_id,
        patient_id,
        provider_id,
        department_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.organization_id,
        patient_rec.id,
        provider_rec.id,
        department_rec.id,
        appointment_date,
        duration_val,
        status_val,
        reason_text,
        notes_text,
        NOW() - INTERVAL '7 days',
        NOW() - INTERVAL '7 days'
      );
    END LOOP;

    -- Create an appointment for today for some patients (1 in 5 chance)
    IF random() < 0.2 THEN
      INSERT INTO public.appointments (
        id,
        organization_id,
        patient_id,
        provider_id,
        department_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.organization_id,
        patient_rec.id,
        provider_rec.id,
        department_rec.id,
        date_trunc('day', NOW()) + ((8 + floor(random() * 8))::integer * INTERVAL '1 hour'),
        30,
        'scheduled',
        'Today''s appointment',
        'Appointment scheduled for today with ' || provider_rec.name,
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '1 day'
      );
    END IF;
  END LOOP;
END
$$;

COMMIT;