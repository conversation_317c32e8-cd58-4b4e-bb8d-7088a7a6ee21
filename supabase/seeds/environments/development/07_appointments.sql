-- Enhanced Appointments with Realistic Medical Scheduling Patterns
BEGIN;

-- Create comprehensive appointments with condition-specific scheduling and realistic medical workflows
WITH patient_condition_data AS (
  SELECT
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.date_of_birth,
    p.medical_history,
    EXTRACT(YEAR FROM AGE(p.date_of_birth)) as age,
    hp.id as provider_id,
    hp.first_name || ' ' || hp.last_name as provider_name,
    hp.provider_type,
    hp.specialization,
    d.id as department_id,
    d.name as department_name,
    d.type as department_type
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  JOIN public.departments d ON d.facility_id IN (
    SELECT f.id FROM public.facilities f WHERE f.organization_id = p.organization_id
  )
  WHERE hp.provider_type IN ('doctor', 'specialist')
),
appointment_templates AS (
  SELECT * FROM (VALUES
    -- Diabetes Management Appointments
    ('<PERSON>', 'Diabetes follow-up', 'Routine diabetes management visit', 30, 'Follow-up for Type 2 diabetes. Review HbA1c results and medication adherence.', 'scheduled'),
    ('<PERSON>', 'Endocrinology consultation', 'Specialist referral for diabetes complications', 60, 'New patient consultation for diabetes management optimization.', 'scheduled'),
    ('<PERSON>', 'Ophthalmology screening', 'Diabetic retinopathy screening', 45, 'Annual diabetic eye exam to screen for retinopathy.', 'scheduled'),

    -- Cardiac Care Appointments
    ('<PERSON> Chen', 'Cardiology follow-up', 'Heart failure management', 45, 'Follow-up for heart failure. Review echo results and medication titration.', 'completed'),
    ('Michael Chen', 'INR check', 'Warfarin monitoring', 15, 'Routine INR monitoring for anticoagulation therapy.', 'completed'),
    ('Michael Chen', 'Cardiac rehabilitation', 'Phase II cardiac rehab', 60, 'Supervised exercise and education session.', 'completed'),

    -- Respiratory Care
    ('Emily Rodriguez', 'Asthma follow-up', 'Asthma control assessment', 30, 'Routine asthma management. Review peak flow diary and inhaler technique.', 'completed'),
    ('Emily Rodriguez', 'Pulmonary function test', 'Spirometry testing', 45, 'Baseline pulmonary function testing for asthma monitoring.', 'scheduled'),
    ('Emily Rodriguez', 'Allergy testing', 'Environmental allergy evaluation', 90, 'Comprehensive allergy testing for asthma triggers.', 'scheduled'),

    -- Oncology Care
    ('Robert Williams', 'Oncology follow-up', 'Post-surgical cancer surveillance', 60, 'Routine oncology follow-up. Review CT scan results and symptom assessment.', 'completed'),
    ('Robert Williams', 'Pulmonary rehabilitation', 'Post-surgical pulmonary rehab', 60, 'Breathing exercises and conditioning post-lobectomy.', 'in_progress'),
    ('Robert Williams', 'CT chest scan', 'Cancer surveillance imaging', 30, 'Routine surveillance CT for lung cancer follow-up.', 'scheduled'),

    -- Type 1 Diabetes Pediatric
    ('Jessica Taylor', 'Endocrinology follow-up', 'Type 1 diabetes management', 45, 'Routine diabetes follow-up. Review CGM data and insulin pump settings.', 'completed'),
    ('Jessica Taylor', 'Diabetes education', 'Carbohydrate counting class', 90, 'Advanced carbohydrate counting and insulin-to-carb ratio adjustment.', 'scheduled'),
    ('Jessica Taylor', 'Ophthalmology screening', 'Diabetic eye screening', 30, 'Annual diabetic retinopathy screening.', 'scheduled'),

    -- Primary Care Appointments
    ('David Anderson', 'Annual physical', 'Comprehensive physical exam', 60, 'Annual wellness visit with health maintenance screening.', 'scheduled'),
    ('David Anderson', 'Blood pressure check', 'Hypertension monitoring', 15, 'Blood pressure recheck after medication adjustment.', 'completed'),
    ('David Anderson', 'Sleep study consultation', 'Sleep apnea evaluation', 45, 'Consultation for sleep study results and CPAP therapy.', 'scheduled'),

    ('Lisa Garcia', 'Postpartum visit', '6-week postpartum check', 30, 'Routine postpartum examination and contraception counseling.', 'completed'),
    ('Lisa Garcia', 'Iron deficiency follow-up', 'Anemia management', 30, 'Follow-up for iron deficiency anemia treatment.', 'scheduled'),
    ('Lisa Garcia', 'Annual gynecologic exam', 'Routine women''s health visit', 45, 'Annual pelvic exam and Pap smear.', 'scheduled'),

    ('Kevin Lee', 'Pain management', 'Chronic back pain evaluation', 45, 'Pain assessment and treatment plan review.', 'completed'),
    ('Kevin Lee', 'Physical therapy evaluation', 'PT assessment for back pain', 60, 'Initial physical therapy evaluation and treatment planning.', 'completed'),
    ('Kevin Lee', 'MRI review', 'Imaging results discussion', 30, 'Review MRI findings and treatment options.', 'scheduled'),

    -- Pediatric Appointments
    ('Emma Garcia', 'Well child visit', '5-year well child check', 45, 'Routine pediatric visit with immunizations and development assessment.', 'completed'),
    ('Emma Garcia', 'Asthma follow-up', 'Pediatric asthma management', 30, 'Asthma control assessment and inhaler technique review.', 'scheduled'),
    ('Emma Garcia', 'Allergy consultation', 'Food allergy management', 60, 'Specialist consultation for food allergy management and EpiPen training.', 'scheduled'),

    ('Jacob Martinez', 'ADHD follow-up', 'Behavioral assessment', 45, 'ADHD medication monitoring and behavioral intervention review.', 'completed'),
    ('Jacob Martinez', 'Speech therapy evaluation', 'Speech delay assessment', 60, 'Comprehensive speech and language evaluation.', 'completed'),
    ('Jacob Martinez', 'Developmental pediatrics', 'Developmental assessment', 90, 'Comprehensive developmental evaluation and intervention planning.', 'scheduled'),

    ('Olivia Smith', 'Diabetes follow-up', 'Type 1 diabetes management', 45, 'Routine diabetes visit with pump download and HbA1c review.', 'completed'),
    ('Olivia Smith', 'Endocrinology follow-up', 'Insulin pump adjustment', 60, 'Pump settings optimization and diabetes education.', 'scheduled'),
    ('Olivia Smith', 'School nurse meeting', 'Diabetes care plan review', 30, 'School diabetes management plan update and staff training.', 'scheduled')
  ) AS t(patient_name, reason, description, duration, notes, status)
)
INSERT INTO public.appointments (
  id,
  organization_id,
  patient_id,
  provider_id,
  department_id,
  appointment_date,
  duration_minutes,
  status,
  reason,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pcd.organization_id,
  pcd.patient_id,
  pcd.provider_id,
  pcd.department_id,
  -- Schedule appointments based on type and status
  CASE
    WHEN at.status = 'completed' THEN
      NOW() - (random() * INTERVAL '90 days')
    WHEN at.status = 'in_progress' THEN
      NOW() - (random() * INTERVAL '2 hours')
    ELSE
      NOW() + (random() * INTERVAL '60 days') + INTERVAL '1 day'
  END as appointment_date,
  at.duration,
  at.status::appointment_status,
  at.reason,
  at.notes || ' Provider: ' || pcd.provider_name || '. Department: ' || pcd.department_name || '.',
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '7 days')
FROM patient_condition_data pcd
JOIN appointment_templates at ON at.patient_name = pcd.patient_name
WHERE pcd.provider_id IS NOT NULL
ORDER BY random();

-- Create additional routine appointments for comprehensive scheduling
INSERT INTO public.appointments (
  id,
  organization_id,
  patient_id,
  provider_id,
  department_id,
  appointment_date,
  duration_minutes,
  status,
  reason,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pcd.organization_id,
  pcd.patient_id,
  pcd.provider_id,
  pcd.department_id,
  -- Create some appointments for today/this week
  CASE
    WHEN random() < 0.1 THEN -- 10% today
      date_trunc('day', NOW()) + ((8 + floor(random() * 8))::integer * INTERVAL '1 hour')
    WHEN random() < 0.3 THEN -- 20% this week
      NOW() + (random() * INTERVAL '7 days')
    ELSE -- 70% future appointments
      NOW() + (random() * INTERVAL '90 days') + INTERVAL '1 day'
  END as appointment_date,
  CASE
    WHEN random() < 0.3 THEN 15  -- Quick visits
    WHEN random() < 0.6 THEN 30  -- Standard visits
    WHEN random() < 0.8 THEN 45  -- Extended visits
    ELSE 60                      -- Comprehensive visits
  END as duration,
  CASE
    WHEN random() < 0.1 THEN 'cancelled'::appointment_status
    WHEN random() < 0.15 THEN 'no_show'::appointment_status
    ELSE 'scheduled'::appointment_status
  END as status,
  CASE (random() * 8)::int
    WHEN 0 THEN 'Annual physical exam'
    WHEN 1 THEN 'Follow-up visit'
    WHEN 2 THEN 'Medication review'
    WHEN 3 THEN 'Lab results review'
    WHEN 4 THEN 'Preventive care visit'
    WHEN 5 THEN 'Chronic disease management'
    WHEN 6 THEN 'Specialist consultation'
    ELSE 'Routine check-up'
  END as reason,
  'Standard appointment with ' || pcd.provider_name || ' in ' || pcd.department_name || ' department.',
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '7 days')
FROM patient_condition_data pcd
WHERE random() < 0.5  -- Create additional appointments for 50% of patient-provider combinations
ORDER BY random();

COMMIT;