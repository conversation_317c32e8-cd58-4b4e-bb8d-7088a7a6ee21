-- Start transaction
BEGIN;

-- Insert Care Team Members
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.care_team_members (
  id,
  patient_id,
  provider_id,
  role,
  start_date,
  end_date,
  primary_contact,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  prov.id as provider_id,
  CASE 
    WHEN prov.name = 'Dr. <PERSON>' THEN 'Primary Care Physician'
    WHEN prov.name = 'Dr. <PERSON>' THEN 'Cardiologist'
    WHEN prov.name = 'Dr. <PERSON>' THEN 'Pediatrician'
    WHEN prov.name = 'Dr. <PERSON>' THEN 'Primary Care Physician'
    WHEN prov.name = 'Dr. <PERSON>' THEN 'Pediatrician'
  END as role,
  NOW() - INTERVAL '1 year' as start_date,
  NULL as end_date,
  CASE 
    WHEN (p.name = '<PERSON>' AND prov.name = 'Dr. <PERSON>') THEN true
    WHEN (p.name = '<PERSON>' AND prov.name = '<PERSON>. <PERSON>') THEN true
    WHEN (p.name = '<PERSON>' AND prov.name = 'Dr. <PERSON>') THEN true
    WHEN (p.name = '<PERSON>' AND prov.name = 'Dr. James') THEN true
    WHEN (p.name = 'Emma Garcia' AND prov.name = 'Dr. Robert') THEN true
    ELSE false
  END as primary_contact,
  CASE 
    WHEN (p.name = 'Alice Thompson' AND prov.name = 'Dr. Sarah') THEN 'Primary care physician managing hypertension and diabetes'
    WHEN (p.name = 'James <PERSON>' AND prov.name = 'Dr. Michael') THEN 'Cardiologist managing coronary artery disease'
    WHEN (p.name = 'Sophia Martinez' AND prov.name = 'Dr. Lisa') THEN 'Pediatrician managing asthma'
    WHEN (p.name = 'David Anderson' AND prov.name = 'Dr. James') THEN 'Primary care physician'
    WHEN (p.name = 'Emma Garcia' AND prov.name = 'Dr. Robert') THEN 'Pediatrician'
    ELSE NULL
  END as notes,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  (p.name = 'Alice Thompson' AND prov.name = 'Dr. Sarah') OR
  (p.name = 'James Wilson' AND prov.name = 'Dr. Michael') OR
  (p.name = 'Sophia Martinez' AND prov.name = 'Dr. Lisa') OR
  (p.name = 'David Anderson' AND prov.name = 'Dr. James') OR
  (p.name = 'Emma Garcia' AND prov.name = 'Dr. Robert');

COMMIT;
