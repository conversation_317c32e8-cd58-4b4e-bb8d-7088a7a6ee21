-- Enhanced Allergies with Comprehensive Medical Allergy Data
BEGIN;

-- Create comprehensive allergy records with realistic medical allergy profiles
WITH patient_data AS (
  SELECT
    id,
    first_name || ' ' || last_name AS name,
    organization_id
  FROM public.patients
),
allergy_data AS (
  SELECT * FROM (VALUES
    -- Drug Allergies
    ('<PERSON>', 'Penicillin', 'Urticaria, pruritus', 'moderate', '2010-06-15', 'active', 'Patient reported during admission'),
    ('<PERSON>', 'Sulfonamides', '<PERSON><PERSON>, <PERSON>-Johnson syndrome', 'severe', '2015-03-22', 'active', 'Documented in previous hospitalization'),

    ('<PERSON>', '<PERSON><PERSON>', 'Anaphylaxis, hypotension, bronchospasm', 'life_threatening', '2005-08-22', 'active', 'Emergency department visit 2005'),
    ('<PERSON>', 'Iodine contrast', 'Urticaria, mild bronchospasm', 'moderate', '2018-11-10', 'active', 'Reaction during cardiac catheterization'),

    ('<PERSON>', 'Latex', 'Contact dermatitis, urticaria', 'moderate', '2015-11-05', 'active', 'Occupational exposure'),
    ('<PERSON>', 'Sulfa drugs', 'Rash, photosensitivity', 'mild', '2020-07-18', 'active', 'Reaction to trimethoprim-sulfamethoxazole'),
    ('Emily Rodriguez', 'Tree pollen', 'Allergic rhinitis, conjunctivitis', 'mild', '2010-04-01', 'active', 'Seasonal environmental allergy'),
    ('<PERSON>', 'Dust mites', 'Asthma exacerbation, rhinitis', 'moderate', '2012-01-15', 'active', 'Confirmed by allergy testing'),

    ('Robert Williams', 'Morphine', 'Nausea, vomiting, pruritus', 'moderate', '2020-09-15', 'active', 'Post-operative reaction'),
    ('Robert Williams', 'Codeine', 'Severe nausea, vomiting', 'moderate', '2018-05-20', 'active', 'Related to morphine allergy'),

    ('Jessica Taylor', 'No known drug allergies', 'N/A', 'mild', NULL, 'inactive', 'No documented drug allergies'),

    ('David Anderson', 'ACE inhibitors', 'Angioedema, dry cough', 'severe', '2019-02-28', 'active', 'Reaction to lisinopril'),
    ('David Anderson', 'Bee stings', 'Local swelling, urticaria', 'moderate', '2010-07-04', 'active', 'Childhood reaction, carries EpiPen'),

    ('Lisa Garcia', 'Codeine', 'Severe nausea, dizziness', 'moderate', '2016-12-10', 'active', 'Post-partum pain management'),
    ('Lisa Garcia', 'Ragweed pollen', 'Allergic rhinitis, sneezing', 'mild', '2008-09-15', 'active', 'Fall seasonal allergies'),

    ('Kevin Lee', 'NSAIDs', 'Gastric irritation, ulceration', 'moderate', '2017-06-12', 'active', 'History of peptic ulcer disease'),
    ('Kevin Lee', 'Aspirin', 'Gastric bleeding', 'severe', '2017-06-12', 'active', 'Related to NSAID allergy'),

    -- Pediatric Allergies
    ('Emma Garcia', 'Eggs', 'Urticaria, vomiting, diarrhea', 'moderate', '2020-01-15', 'active', 'Food allergy confirmed by testing'),
    ('Emma Garcia', 'Tree nuts', 'Anaphylaxis, respiratory distress', 'life_threatening', '2021-03-08', 'active', 'Severe reaction, carries EpiPen'),
    ('Emma Garcia', 'Peanuts', 'Urticaria, mild respiratory symptoms', 'moderate', '2021-03-08', 'active', 'Cross-reactivity with tree nuts'),
    ('Emma Garcia', 'Cat dander', 'Asthma exacerbation, rhinitis', 'moderate', '2019-11-20', 'active', 'Environmental allergy'),

    ('Jacob Martinez', 'No known allergies', 'N/A', 'mild', NULL, 'inactive', 'No documented allergies to date'),

    ('Olivia Smith', 'No known drug allergies', 'N/A', 'mild', NULL, 'inactive', 'No documented drug allergies'),
    ('Olivia Smith', 'Grass pollen', 'Allergic rhinitis, mild asthma', 'mild', '2022-05-15', 'active', 'Spring seasonal allergies')
  ) AS t(patient_name, allergen, reaction, severity, onset_date, status, reported_by)
)
INSERT INTO public.allergies (
  id,
  patient_id,
  allergen,
  reaction,
  severity,
  onset_date,
  status,
  reported_by,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pd.id as patient_id,
  ad.allergen,
  ad.reaction,
  ad.severity::allergy_severity,
  CASE
    WHEN ad.onset_date IS NOT NULL THEN ad.onset_date::date
    ELSE NULL
  END as onset_date,
  ad.status::allergy_status,
  ad.reported_by,
  NOW() - (random() * INTERVAL '1 year'),
  NOW() - (random() * INTERVAL '30 days')
FROM patient_data pd
JOIN allergy_data ad ON ad.patient_name = pd.name;

COMMIT;
