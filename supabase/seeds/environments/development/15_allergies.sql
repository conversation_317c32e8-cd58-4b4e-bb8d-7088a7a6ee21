-- Start transaction
BEGIN;

-- Insert Allergies
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
)
INSERT INTO public.allergies (id, patient_id, allergen, reaction, severity, onset_date, status, reported_by)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'Penicillin'
    WHEN '<PERSON>' THEN 'Shellfish'
    WHEN '<PERSON>' THEN 'Peanuts'
    WHEN '<PERSON>' THEN 'Latex'
    WHEN '<PERSON>' THEN 'Eggs'
    ELSE 'Unknown'
  END as allergen,
  CASE p.name
    WHEN '<PERSON>' THEN 'Rash, hives'
    WHEN '<PERSON>' THEN 'Anaphylaxis'
    WHEN '<PERSON>' THEN 'Swelling, difficulty breathing'
    WHEN '<PERSON>' THEN 'Contact dermatitis'
    WHEN '<PERSON>' THEN 'Hives, stomach pain'
    ELSE 'Unknown reaction'
  END as reaction,
  CASE p.name
    WHEN '<PERSON>' THEN 'moderate'::allergy_severity
    WHEN '<PERSON>' THEN 'severe'::allergy_severity
    WHEN '<PERSON>' THEN 'life_threatening'::allergy_severity
    WHEN '<PERSON>' THEN 'mild'::allergy_severity
    WHEN '<PERSON>' THEN 'moderate'::allergy_severity
    ELSE 'mild'::allergy_severity
  END as severity,
  CASE p.name
    WHEN 'Alice Thompson' THEN '2010-06-15'::date
    WHEN 'James Wilson' THEN '2005-08-22'::date
    WHEN 'Sophia Martinez' THEN '2018-03-10'::date
    WHEN 'David Anderson' THEN '2015-11-05'::date
    WHEN '<PERSON>' THEN '2020-01-15'::date
    ELSE (NOW() - (random() * INTERVAL '5 years'))::date
  END as onset_date,
  'active' as status,
  NULL as reported_by
FROM patient_data p;

COMMIT;
