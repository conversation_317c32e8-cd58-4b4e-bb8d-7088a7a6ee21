-- Enhanced Medications with Realistic Pharmaceutical Data and NDC Codes
BEGIN;

-- Create comprehensive medication records with proper drug information
WITH patient_provider_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    hp.id as prescribing_provider_id,
    p.medical_history
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  WHERE hp.provider_type IN ('doctor', 'specialist')
),
medication_data AS (
  SELECT * FROM (VALUES
    -- Diabetes Medications
    ('<PERSON>', 'Metformin', 'metformin hydrochloride', '1000', 'mg', 'tablet', 'oral', 'twice daily', 
     '0093-1074-01', 'Take with meals to reduce GI upset', 'Type 2 diabetes management', 90, 2, 'active'),
    ('<PERSON>', 'Lisinopril', 'lisinopril', '10', 'mg', 'tablet', 'oral', 'once daily',
     '0071-0222-23', 'Monitor blood pressure and potassium levels', 'Hypertension', 90, 2, 'active'),
    ('<PERSON>', 'Atorvastatin', 'atorvastatin calcium', '20', 'mg', 'tablet', 'oral', 'once daily at bedtime',
     '0071-0155-23', 'Take at bedtime. Monitor liver function', 'Hyperlipidemia', 90, 2, 'active'),
    
    -- Cardiac Medications
    ('Michael Chen', 'Carvedilol', 'carvedilol', '3.125', 'mg', 'tablet', 'oral', 'twice daily',
     '0378-0087-01', 'Take with food. Do not stop abruptly', 'Heart failure', 60, 1, 'active'),
    ('Michael Chen', 'Furosemide', 'furosemide', '40', 'mg', 'tablet', 'oral', 'once daily',
     '0054-0265-25', 'Take in morning. Monitor electrolytes', 'Heart failure', 30, 2, 'active'),
    ('Michael Chen', 'Warfarin', 'warfarin sodium', '5', 'mg', 'tablet', 'oral', 'once daily',
     '0093-0312-01', 'Monitor INR regularly. Avoid vitamin K rich foods', 'Atrial fibrillation', 30, 0, 'active'),
    ('Michael Chen', 'Atorvastatin', 'atorvastatin calcium', '80', 'mg', 'tablet', 'oral', 'once daily',
     '0071-0156-23', 'High intensity statin for CAD', 'Coronary artery disease', 90, 2, 'active'),
    
    -- Respiratory Medications
    ('Emily Rodriguez', 'Albuterol HFA', 'albuterol sulfate', '90', 'mcg', 'inhaler', 'inhalation', 'as needed',
     '0173-0682-20', 'Rescue inhaler. Rinse mouth after use', 'Asthma', 1, 5, 'active'),
    ('Emily Rodriguez', 'Fluticasone/Salmeterol', 'fluticasone/salmeterol', '250/50', 'mcg', 'inhaler', 'inhalation', 'twice daily',
     '0173-0715-20', 'Controller medication. Rinse mouth after use', 'Asthma', 1, 2, 'active'),
    ('Emily Rodriguez', 'Omeprazole', 'omeprazole', '20', 'mg', 'capsule', 'oral', 'once daily before breakfast',
     '0093-7663-56', 'Take 30 minutes before first meal', 'GERD', 90, 2, 'active'),
    ('Emily Rodriguez', 'Sertraline', 'sertraline hydrochloride', '50', 'mg', 'tablet', 'oral', 'once daily',
     '0093-7146-56', 'May take 4-6 weeks for full effect', 'Anxiety disorder', 90, 2, 'active'),
    
    -- COPD and Cancer Medications
    ('Robert Williams', 'Tiotropium', 'tiotropium bromide', '18', 'mcg', 'inhaler', 'inhalation', 'once daily',
     '0597-0075-41', 'Long-acting bronchodilator for COPD', 'COPD', 30, 2, 'active'),
    ('Robert Williams', 'Prednisone', 'prednisone', '10', 'mg', 'tablet', 'oral', 'once daily',
     '0054-0220-25', 'Take with food. Taper slowly when discontinuing', 'COPD exacerbation', 30, 1, 'active'),
    ('Robert Williams', 'Sertraline', 'sertraline hydrochloride', '50', 'mg', 'tablet', 'oral', 'once daily',
     '0093-7146-56', 'Monitor for mood changes', 'Depression', 90, 2, 'active'),
    
    -- Type 1 Diabetes Medications
    ('Jessica Taylor', 'Insulin Aspart', 'insulin aspart', '100', 'units/mL', 'pen', 'subcutaneous', 'with meals',
     '0169-7501-11', 'Rapid-acting insulin. Rotate injection sites', 'Type 1 diabetes', 5, 2, 'active'),
    ('Jessica Taylor', 'Insulin Glargine', 'insulin glargine', '100', 'units/mL', 'pen', 'subcutaneous', 'once daily at bedtime',
     '0088-2220-33', 'Long-acting insulin. Same time daily', 'Type 1 diabetes', 5, 2, 'active'),
    
    -- Primary Care Medications
    ('David Anderson', 'Amlodipine', 'amlodipine besylate', '5', 'mg', 'tablet', 'oral', 'once daily',
     '0093-1041-01', 'May cause ankle swelling', 'Hypertension', 90, 2, 'active'),
    ('David Anderson', 'Hydrochlorothiazide', 'hydrochlorothiazide', '25', 'mg', 'tablet', 'oral', 'once daily',
     '0054-0062-25', 'Take in morning. Monitor electrolytes', 'Hypertension', 90, 2, 'active'),
    
    ('Lisa Garcia', 'Ferrous Sulfate', 'ferrous sulfate', '325', 'mg', 'tablet', 'oral', 'three times daily',
     '0113-0486-78', 'Take on empty stomach. May cause constipation', 'Iron deficiency anemia', 90, 2, 'active'),
    ('Lisa Garcia', 'Prenatal Vitamins', 'prenatal multivitamin', '1', 'tablet', 'tablet', 'oral', 'once daily',
     '0536-1008-01', 'Continue while breastfeeding', 'Nutritional support', 90, 2, 'active'),
    
    ('Kevin Lee', 'Gabapentin', 'gabapentin', '300', 'mg', 'capsule', 'oral', 'three times daily',
     '0093-0130-01', 'May cause drowsiness. Increase gradually', 'Neuropathic pain', 90, 2, 'active'),
    ('Kevin Lee', 'Topiramate', 'topiramate', '25', 'mg', 'tablet', 'oral', 'once daily',
     '0093-5441-56', 'Increase fluid intake. Monitor for kidney stones', 'Migraine prophylaxis', 90, 2, 'active'),
    
    -- Pediatric Medications
    ('Emma Garcia', 'Albuterol HFA', 'albuterol sulfate', '90', 'mcg', 'inhaler', 'inhalation', 'as needed',
     '0173-0682-20', 'Use with spacer device. Parent supervised', 'Asthma', 1, 5, 'active'),
    ('Emma Garcia', 'Fluticasone', 'fluticasone propionate', '44', 'mcg', 'inhaler', 'inhalation', 'twice daily',
     '0173-0452-20', 'Controller medication. Use spacer', 'Asthma', 1, 2, 'active'),
    ('Emma Garcia', 'Hydrocortisone Cream', 'hydrocortisone', '1', '%', 'cream', 'topical', 'twice daily as needed',
     '0168-0011-30', 'Apply thin layer to affected areas', 'Eczema', 1, 2, 'active'),
    
    ('Jacob Martinez', 'Methylphenidate', 'methylphenidate hydrochloride', '5', 'mg', 'tablet', 'oral', 'twice daily',
     '0054-0092-25', 'Give with breakfast and lunch. Monitor growth', 'ADHD', 30, 1, 'active'),
    
    ('Olivia Smith', 'Insulin Aspart', 'insulin aspart', '100', 'units/mL', 'pen', 'subcutaneous', 'with meals via pump',
     '0169-7501-11', 'Continuous subcutaneous insulin infusion', 'Type 1 diabetes', 5, 2, 'active'),
    ('Olivia Smith', 'Glucagon', 'glucagon', '1', 'mg', 'kit', 'intramuscular', 'emergency use only',
     '0002-8801-01', 'For severe hypoglycemia. Train caregivers', 'Hypoglycemia emergency', 1, 0, 'active')
  ) AS t(patient_name, medication_name, generic_name, dosage, unit, form, route, frequency, ndc_code, instructions, indication, quantity, refills, status)
)
INSERT INTO public.medications (
  id,
  organization_id,
  patient_id,
  prescribing_provider_id,
  medication_name,
  generic_name,
  dosage,
  unit,
  form,
  route,
  frequency,
  quantity,
  refills_remaining,
  prescribed_date,
  start_date,
  end_date,
  status,
  instructions,
  indication,
  ndc_code,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  ppd.organization_id,
  ppd.patient_id,
  ppd.prescribing_provider_id,
  md.medication_name,
  md.generic_name,
  md.dosage,
  md.unit,
  md.form,
  md.route,
  md.frequency,
  md.quantity::integer,
  md.refills::integer,
  NOW() - (random() * INTERVAL '180 days'),
  NOW() - (random() * INTERVAL '180 days'),
  CASE 
    WHEN md.status = 'active' THEN NOW() + INTERVAL '90 days'
    ELSE NOW() - (random() * INTERVAL '30 days')
  END,
  md.status,
  md.instructions,
  md.indication,
  md.ndc_code,
  NOW() - (random() * INTERVAL '180 days'),
  NOW() - (random() * INTERVAL '30 days')
FROM patient_provider_data ppd
JOIN medication_data md ON md.patient_name = ppd.patient_name;

COMMIT;
