-- Start transaction
BEGIN;

-- Insert Patients using CTEs
WITH patient_data (name, id) AS (
  VALUES
    -- Spritely Medical Center patients
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),

    -- Spritely Community Clinic patients
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),

    -- Spritely Pediatrics patients
    ('<PERSON>', uuid_generate_v4()),
    ('Jacob Martinez', uuid_generate_v4()),
    ('<PERSON>', uuid_generate_v4()),
    ('Ethan Johnson', uuid_generate_v4()),
    ('Ava Williams', uuid_generate_v4()),
    ('Noah Brown', uuid_generate_v4()),

    -- Northside Regional Hospital patients
    ('William Scott', uuid_generate_v4()),
    ('Michelle Lee', uuid_generate_v4()),
    ('Brandon Turner', uuid_generate_v4()),
    ('Rachel Green', uuid_generate_v4()),
    ('Steven Cooper', uuid_generate_v4()),

    -- Valley Cardiology Associates patients
    ('Patricia Moore', uuid_generate_v4()),
    ('Richard Adams', uuid_generate_v4()),
    ('Elizabeth White', uuid_generate_v4()),
    ('George Miller', uuid_generate_v4()),
    ('Helen Davis', uuid_generate_v4())
),
org_data AS (
  SELECT id, name FROM public.organizations
)
INSERT INTO public.patients (
  id,
  organization_id,
  first_name,
  last_name,
  date_of_birth,
  gender,
  phone,
  email,
  address,
  emergency_contact,
  insurance_info,
  medical_history,
  created_at,
  updated_at
)
SELECT
  p.id,
  o.id as organization_id,
  split_part(p.name, ' ', 1) as first_name,
  split_part(p.name, ' ', 2) as last_name,
  CASE
    -- Spritely Medical Center patients
    WHEN p.name = 'Alice Thompson' THEN '1985-03-15'::date
    WHEN p.name = 'James Wilson' THEN '1978-07-22'::date
    WHEN p.name = 'Sophia Martinez' THEN '2015-11-10'::date
    WHEN p.name = 'Michael Johnson' THEN '1982-09-18'::date
    WHEN p.name = 'Emily Rodriguez' THEN '1990-04-25'::date
    WHEN p.name = 'Robert Chen' THEN '1975-12-03'::date
    WHEN p.name = 'Sarah Williams' THEN '1988-06-14'::date
    WHEN p.name = 'Thomas Brown' THEN '1979-08-30'::date
    WHEN p.name = 'Jessica Taylor' THEN '1995-02-17'::date
    WHEN p.name = 'Daniel Jackson' THEN '1983-11-22'::date

    -- Spritely Community Clinic patients
    WHEN p.name = 'David Anderson' THEN '1992-05-03'::date
    WHEN p.name = 'Lisa Garcia' THEN '1987-07-19'::date
    WHEN p.name = 'Kevin Lee' THEN '1980-01-28'::date
    WHEN p.name = 'Amanda Wilson' THEN '1993-10-12'::date
    WHEN p.name = 'James Thompson' THEN '1976-03-05'::date
    WHEN p.name = 'Maria Hernandez' THEN '1989-09-23'::date
    WHEN p.name = 'Christopher Davis' THEN '1994-11-08'::date
    WHEN p.name = 'Nicole Wright' THEN '1984-06-30'::date

    -- Spritely Pediatrics patients
    WHEN p.name = 'Emma Garcia' THEN '2018-02-28'::date
    WHEN p.name = 'Jacob Martinez' THEN '2019-05-14'::date
    WHEN p.name = 'Olivia Smith' THEN '2017-09-03'::date
    WHEN p.name = 'Ethan Johnson' THEN '2020-01-22'::date
    WHEN p.name = 'Ava Williams' THEN '2018-11-17'::date
    WHEN p.name = 'Noah Brown' THEN '2019-08-09'::date

    -- Northside Regional Hospital patients
    WHEN p.name = 'William Scott' THEN '1972-04-18'::date
    WHEN p.name = 'Michelle Lee' THEN '1981-12-07'::date
    WHEN p.name = 'Brandon Turner' THEN '1990-03-25'::date
    WHEN p.name = 'Rachel Green' THEN '1986-08-14'::date
    WHEN p.name = 'Steven Cooper' THEN '1977-05-30'::date

    -- Valley Cardiology Associates patients
    WHEN p.name = 'Patricia Moore' THEN '1965-09-12'::date
    WHEN p.name = 'Richard Adams' THEN '1958-11-03'::date
    WHEN p.name = 'Elizabeth White' THEN '1970-07-22'::date
    WHEN p.name = 'George Miller' THEN '1962-04-15'::date
    WHEN p.name = 'Helen Davis' THEN '1968-01-28'::date

    ELSE '2000-01-01'::date -- Default fallback
  END as date_of_birth,
  CASE
    -- Spritely Medical Center patients
    WHEN p.name = 'Alice Thompson' THEN 'female'::gender
    WHEN p.name = 'James Wilson' THEN 'male'::gender
    WHEN p.name = 'Sophia Martinez' THEN 'female'::gender
    WHEN p.name = 'Michael Johnson' THEN 'male'::gender
    WHEN p.name = 'Emily Rodriguez' THEN 'female'::gender
    WHEN p.name = 'Robert Chen' THEN 'male'::gender
    WHEN p.name = 'Sarah Williams' THEN 'female'::gender
    WHEN p.name = 'Thomas Brown' THEN 'male'::gender
    WHEN p.name = 'Jessica Taylor' THEN 'female'::gender
    WHEN p.name = 'Daniel Jackson' THEN 'male'::gender

    -- Spritely Community Clinic patients
    WHEN p.name = 'David Anderson' THEN 'male'::gender
    WHEN p.name = 'Lisa Garcia' THEN 'female'::gender
    WHEN p.name = 'Kevin Lee' THEN 'male'::gender
    WHEN p.name = 'Amanda Wilson' THEN 'female'::gender
    WHEN p.name = 'James Thompson' THEN 'male'::gender
    WHEN p.name = 'Maria Hernandez' THEN 'female'::gender
    WHEN p.name = 'Christopher Davis' THEN 'male'::gender
    WHEN p.name = 'Nicole Wright' THEN 'female'::gender

    -- Spritely Pediatrics patients
    WHEN p.name = 'Emma Garcia' THEN 'female'::gender
    WHEN p.name = 'Jacob Martinez' THEN 'male'::gender
    WHEN p.name = 'Olivia Smith' THEN 'female'::gender
    WHEN p.name = 'Ethan Johnson' THEN 'male'::gender
    WHEN p.name = 'Ava Williams' THEN 'female'::gender
    WHEN p.name = 'Noah Brown' THEN 'male'::gender

    -- Northside Regional Hospital patients
    WHEN p.name = 'William Scott' THEN 'male'::gender
    WHEN p.name = 'Michelle Lee' THEN 'female'::gender
    WHEN p.name = 'Brandon Turner' THEN 'male'::gender
    WHEN p.name = 'Rachel Green' THEN 'female'::gender
    WHEN p.name = 'Steven Cooper' THEN 'male'::gender

    -- Valley Cardiology Associates patients
    WHEN p.name = 'Patricia Moore' THEN 'female'::gender
    WHEN p.name = 'Richard Adams' THEN 'male'::gender
    WHEN p.name = 'Elizabeth White' THEN 'female'::gender
    WHEN p.name = 'George Miller' THEN 'male'::gender
    WHEN p.name = 'Helen Davis' THEN 'female'::gender

    ELSE 'other'::gender -- Default fallback
  END as gender,
  CASE p.name
    WHEN 'Alice Thompson' THEN '************'
    WHEN 'James Wilson' THEN '************'
    WHEN 'Sophia Martinez' THEN '************'
    WHEN 'David Anderson' THEN '************'
    WHEN 'Emma Garcia' THEN '************'
  END as phone,
  CASE p.name
    WHEN 'Alice Thompson' THEN '<EMAIL>'
    WHEN 'James Wilson' THEN '<EMAIL>'
    WHEN 'Sophia Martinez' THEN '<EMAIL>'
    WHEN 'David Anderson' THEN '<EMAIL>'
    WHEN 'Emma Garcia' THEN '<EMAIL>'
  END as email,
  CASE p.name
    WHEN 'Alice Thompson' THEN '123 Main St, Anytown, USA'
    WHEN 'James Wilson' THEN '456 Oak Ave, Anytown, USA'
    WHEN 'Sophia Martinez' THEN '789 Pine Rd, Anytown, USA'
    WHEN 'David Anderson' THEN '101 Elm St, Othertown, USA'
    WHEN 'Emma Garcia' THEN '202 Maple Dr, Othertown, USA'
  END as address,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Bob Thompson (Husband): ************'
    WHEN 'James Wilson' THEN 'Mary Wilson (Wife): ************'
    WHEN 'Sophia Martinez' THEN 'Carlos Martinez (Father): ************'
    WHEN 'David Anderson' THEN 'Lisa Anderson (Sister): ************'
    WHEN 'Emma Garcia' THEN 'Luis Garcia (Father): ************'
  END as emergency_contact,
  CASE p.name
    WHEN 'Alice Thompson' THEN '{"provider": "Blue Cross", "policy_number": "BC12345678", "group_number": "GRP123456"}'::jsonb
    WHEN 'James Wilson' THEN '{"provider": "Aetna", "policy_number": "AE87654321", "group_number": "GRP654321"}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"provider": "United Healthcare", "policy_number": "UH24681357", "group_number": "GRP135792"}'::jsonb
    WHEN 'David Anderson' THEN '{"provider": "Cigna", "policy_number": "CI13579246", "group_number": "GRP246813"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"provider": "Humana", "policy_number": "HU97531246", "group_number": "GRP975312"}'::jsonb
  END as insurance_info,
  CASE p.name
    WHEN 'Alice Thompson' THEN '{"conditions": ["Hypertension", "Type 2 Diabetes"], "surgeries": ["Appendectomy (2010)"]}'::jsonb
    WHEN 'James Wilson' THEN '{"conditions": ["Coronary Artery Disease"], "surgeries": ["Knee Arthroscopy (2015)"]}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"conditions": ["Asthma"], "vaccinations": ["Up to date per CDC schedule"]}'::jsonb
    WHEN 'David Anderson' THEN '{"conditions": ["Seasonal Allergies"], "family_history": ["Father: Hypertension", "Mother: Breast Cancer"]}'::jsonb
    WHEN 'Emma Garcia' THEN '{"conditions": [], "vaccinations": ["Up to date per CDC schedule"]}'::jsonb
  END as medical_history,
  NOW(),
  NOW()
FROM patient_data p
JOIN org_data o ON
  CASE
    -- Spritely Medical Center patients
    WHEN p.name IN ('Alice Thompson', 'James Wilson', 'Sophia Martinez', 'Michael Johnson', 'Emily Rodriguez',
                   'Robert Chen', 'Sarah Williams', 'Thomas Brown', 'Jessica Taylor', 'Daniel Jackson')
         THEN o.name = 'Spritely Medical Center'

    -- Spritely Community Clinic patients
    WHEN p.name IN ('David Anderson', 'Lisa Garcia', 'Kevin Lee', 'Amanda Wilson', 'James Thompson',
                   'Maria Hernandez', 'Christopher Davis', 'Nicole Wright')
         THEN o.name = 'Spritely Community Clinic'

    -- Spritely Pediatrics patients
    WHEN p.name IN ('Emma Garcia', 'Jacob Martinez', 'Olivia Smith', 'Ethan Johnson', 'Ava Williams', 'Noah Brown')
         THEN o.name = 'Spritely Pediatrics'

    -- Northside Regional Hospital patients
    WHEN p.name IN ('William Scott', 'Michelle Lee', 'Brandon Turner', 'Rachel Green', 'Steven Cooper')
         THEN o.name = 'Northside Regional Hospital'

    -- Valley Cardiology Associates patients
    WHEN p.name IN ('Patricia Moore', 'Richard Adams', 'Elizabeth White', 'George Miller', 'Helen Davis')
         THEN o.name = 'Valley Cardiology Associates'
  END;

COMMIT;