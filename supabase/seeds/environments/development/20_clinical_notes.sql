-- Start transaction
BEGIN;

-- Insert Clinical Notes
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
  WHERE first_name || ' ' || last_name IN (
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  )
),
medical_record_data AS (
  SELECT mr.id, mr.patient_id, p.name
  FROM public.medical_records mr
  JOIN patient_data p ON mr.patient_id = p.id
)
INSERT INTO public.clinical_notes (
  id,
  medical_record_id,
  note_type,
  content,
  signed_by,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  mr.id,
  CASE mr.name
    WHEN '<PERSON>' THEN 'progress_note'
    WHEN '<PERSON>' THEN 'consultation'
    WHEN '<PERSON>' THEN 'progress_note'
    WHEN '<PERSON>' THEN 'sick_visit'
    WHEN '<PERSON>' THEN 'well_child'
  END as note_type,
  CASE mr.name
    WHEN '<PERSON>' THEN 'Patient presents with controlled hypertension. BP 120/80. Continue current medication regimen.'
    WH<PERSON> '<PERSON>' THEN 'Cardiology consultation for CAD. Patient reports occasional chest pain with exertion. ECG shows normal sinus rhythm.'
    WHEN '<PERSON>' THEN 'Well-child visit. Growth and development appropriate for age. Asthma well-controlled with current medications.'
    WHEN 'David <PERSON>' THEN 'Patient presents with upper respiratory symptoms. Lungs clear. Likely viral infection. Supportive care recommended.'
    WHEN 'Emma Garcia' THEN 'Well-child check at 5 years. All developmental milestones met. Vaccinations up to date.'
  END as content,
  NULL as signed_by,
  NOW(),
  NOW()
FROM medical_record_data mr;

COMMIT;
