-- Start transaction
BEGIN;

-- Insert Medications
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.medications (
  id,
  patient_id,
  provider_id,
  medication_name,
  dosage,
  frequency,
  start_date,
  end_date,
  active,
  instructions,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  prov.id as provider_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'Lisinopril'
    WHEN '<PERSON>' THEN 'Atorvastatin'
    WHEN '<PERSON>' THEN 'Amoxicillin'
    WHEN '<PERSON>' THEN 'Albuterol'
    WHEN '<PERSON>' THEN 'Cetirizine'
  END as medication_name,
  CASE p.name
    WHEN '<PERSON>' THEN '10mg'
    WHEN '<PERSON>' THEN '20mg'
    WHEN '<PERSON>' THEN '250mg/5mL'
    WHEN '<PERSON>' THEN '90mcg'
    WHEN '<PERSON>' THEN '5mg/5mL'
  END as dosage,
  CASE p.name
    WHEN '<PERSON>' THEN 'Once daily'
    WHEN '<PERSON>' THEN 'Once daily at bedtime'
    WHEN '<PERSON>' THEN 'Twice daily for 10 days'
    WHEN '<PERSON>' THE<PERSON> '2 puffs every 4-6 hours as needed'
    WHEN 'Emma <PERSON>' THEN 'Once daily'
  E<PERSON> as frequency,
  CASE p.name
    WHEN 'Alice Thompson' THEN '2022-01-15'::date
    WHEN 'James Wilson' THEN '2021-11-10'::date
    WHEN 'Sophia Martinez' THEN '2023-02-15'::date
    WHEN 'David Anderson' THEN '2022-09-20'::date
    WHEN 'Emma Garcia' THEN '2023-03-01'::date
  END as start_date,
  CASE p.name
    WHEN 'Alice Thompson' THEN NULL
    WHEN 'James Wilson' THEN NULL
    WHEN 'Sophia Martinez' THEN '2023-02-25'::date
    WHEN 'David Anderson' THEN NULL
    WHEN 'Emma Garcia' THEN NULL
  END as end_date,
  CASE p.name
    WHEN 'Alice Thompson' THEN true
    WHEN 'James Wilson' THEN true
    WHEN 'Sophia Martinez' THEN false
    WHEN 'David Anderson' THEN true
    WHEN 'Emma Garcia' THEN true
  END as active,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Take with water in the morning'
    WHEN 'James Wilson' THEN 'Take at night before bed'
    WHEN 'Sophia Martinez' THEN 'Take with food. Complete full course.'
    WHEN 'David Anderson' THEN 'Use as needed for shortness of breath'
    WHEN 'Emma Garcia' THEN 'Give in the morning for allergy symptoms'
  END as instructions,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN prov.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN prov.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN prov.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN prov.name = 'Dr. Robert'
  END;

COMMIT;
