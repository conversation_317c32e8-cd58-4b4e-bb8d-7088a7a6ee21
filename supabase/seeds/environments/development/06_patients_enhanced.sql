-- Enhanced Comprehensive Patient Data with Realistic Medical Information
BEGIN;

-- Create comprehensive patient data with realistic demographics and medical histories
WITH patient_demographics AS (
  SELECT * FROM (VALUES
    -- Spritely Medical Center - Academic Medical Center Patients (Complex Cases)
    ('<PERSON>', '<PERSON>', '1965-03-15', 'female', '<EMAIL>', '555-0101', 
     '1234 Oak Street, Springfield, IL 62701', '<PERSON> (<PERSON>sband): 555-0102',
     '{"provider": "Blue Cross Blue Shield", "policy_number": "BC123456789", "group_number": "GRP001", "member_id": "SJ123456"}',
     '{"conditions": ["Type 2 Diabetes Mellitus", "Hypertension", "Hyperlipidemia"], "allergies": ["Penicillin"], "surgeries": ["Cholecystectomy (2018)", "Appendectomy (1995)"], "family_history": ["Father: CAD", "Mother: Diabetes"], "social_history": {"smoking": "former", "alcohol": "occasional", "exercise": "sedentary"}}'),
    
    ('<PERSON>', '<PERSON>', '1978-07-22', 'male', 'micha<PERSON>.<EMAIL>', '555-0103',
     '5678 Maple Avenue, Springfield, IL 62702', 'Lisa Chen (Wife): 555-0104',
     '{"provider": "Aetna", "policy_number": "AE987654321", "group_number": "GRP002", "member_id": "MC987654"}',
     '{"conditions": ["Coronary Artery Disease", "Atrial Fibrillation", "Heart Failure"], "allergies": ["Shellfish", "Iodine"], "surgeries": ["CABG x3 (2020)", "Pacemaker Insertion (2021)"], "family_history": ["Father: MI at 55", "Mother: Stroke"], "social_history": {"smoking": "never", "alcohol": "none", "exercise": "cardiac rehab"}}'),
    
    ('Emily', 'Rodriguez', '1990-04-25', 'female', '<EMAIL>', '555-0105',
     '9012 Pine Street, Springfield, IL 62703', 'Carlos Rodriguez (Brother): 555-0106',
     '{"provider": "United Healthcare", "policy_number": "UH456789123", "group_number": "GRP003", "member_id": "ER456789"}',
     '{"conditions": ["Asthma", "Anxiety Disorder", "GERD"], "allergies": ["Latex", "Sulfa"], "surgeries": [], "family_history": ["Mother: Asthma", "Sister: Anxiety"], "social_history": {"smoking": "never", "alcohol": "social", "exercise": "regular"}}'),
    
    ('Robert', 'Williams', '1955-12-03', 'male', '<EMAIL>', '555-0107',
     '3456 Elm Drive, Springfield, IL 62704', 'Margaret Williams (Wife): 555-0108',
     '{"provider": "Medicare + Supplement", "policy_number": "MC789123456", "group_number": "SUPP001", "member_id": "RW789123"}',
     '{"conditions": ["COPD", "Lung Cancer Stage IIIA", "Depression"], "allergies": ["Morphine"], "surgeries": ["Right Upper Lobectomy (2022)", "Hernia Repair (2015)"], "family_history": ["Father: Lung Cancer", "Brother: COPD"], "social_history": {"smoking": "60 pack-years, quit 2022", "alcohol": "former heavy", "exercise": "pulmonary rehab"}}'),
    
    ('Jessica', 'Taylor', '1995-02-17', 'female', '<EMAIL>', '555-0109',
     '7890 Cedar Lane, Springfield, IL 62705', 'David Taylor (Father): 555-0110',
     '{"provider": "Cigna", "policy_number": "CI321654987", "group_number": "GRP004", "member_id": "JT321654"}',
     '{"conditions": ["Type 1 Diabetes", "Diabetic Retinopathy", "Celiac Disease"], "allergies": ["Gluten"], "surgeries": ["Laser Photocoagulation (2021)"], "family_history": ["Grandmother: Type 1 DM"], "social_history": {"smoking": "never", "alcohol": "none", "exercise": "active"}}'),
    
    -- Spritely Community Clinic - Primary Care Patients (Common Conditions)
    ('David', 'Anderson', '1982-05-03', 'male', '<EMAIL>', '555-0201',
     '2468 First Street, Riverside, IL 62801', 'Amanda Anderson (Wife): 555-0202',
     '{"provider": "Medicaid", "policy_number": "MD135792468", "group_number": "MCD001", "member_id": "DA135792"}',
     '{"conditions": ["Hypertension", "Obesity", "Sleep Apnea"], "allergies": ["NKDA"], "surgeries": [], "family_history": ["Father: HTN", "Mother: DM"], "social_history": {"smoking": "current 1ppd", "alcohol": "weekend", "exercise": "minimal"}}'),
    
    ('Lisa', 'Garcia', '1987-07-19', 'female', '<EMAIL>', '555-0203',
     '1357 Second Avenue, Riverside, IL 62802', 'Miguel Garcia (Husband): 555-0204',
     '{"provider": "Humana", "policy_number": "HU864209753", "group_number": "GRP005", "member_id": "LG864209"}',
     '{"conditions": ["Gestational Diabetes (resolved)", "Iron Deficiency Anemia"], "allergies": ["Codeine"], "surgeries": ["C-Section x2 (2015, 2018)"], "family_history": ["Mother: GDM", "Sister: Anemia"], "social_history": {"smoking": "never", "alcohol": "none", "exercise": "moderate"}}'),
    
    ('Kevin', 'Lee', '1980-01-28', 'male', '<EMAIL>', '555-0205',
     '9753 Third Street, Riverside, IL 62803', 'Jennifer Lee (Wife): 555-0206',
     '{"provider": "Blue Cross", "policy_number": "BC975318642", "group_number": "GRP006", "member_id": "KL975318"}',
     '{"conditions": ["Chronic Low Back Pain", "Migraine Headaches"], "allergies": ["NSAIDs"], "surgeries": ["L4-L5 Discectomy (2019)"], "family_history": ["Father: Back problems"], "social_history": {"smoking": "former", "alcohol": "occasional", "exercise": "physical therapy"}}'),
    
    -- Spritely Pediatrics - Pediatric Patients
    ('Emma', 'Garcia', '2018-02-28', 'female', '<EMAIL>', '555-0301',
     '4682 Pediatric Way, Kidstown, IL 62901', 'Luis Garcia (Father): 555-0302',
     '{"provider": "CHIP", "policy_number": "CH147258369", "group_number": "CHIP001", "member_id": "EG147258"}',
     '{"conditions": ["Asthma", "Eczema"], "allergies": ["Eggs", "Tree Nuts"], "surgeries": [], "family_history": ["Mother: Asthma", "Father: Allergies"], "developmental": {"milestones": "appropriate", "immunizations": "up to date"}}'),
    
    ('Jacob', 'Martinez', '2019-05-14', 'male', '<EMAIL>', '555-0303',
     '8520 Children Circle, Kidstown, IL 62902', 'Maria Martinez (Mother): 555-0304',
     '{"provider": "Medicaid", "policy_number": "MD963741852", "group_number": "MCD002", "member_id": "JM963741"}',
     '{"conditions": ["ADHD", "Speech Delay"], "allergies": ["NKDA"], "surgeries": [], "family_history": ["Father: ADHD"], "developmental": {"speech_therapy": true, "immunizations": "up to date"}}'),
    
    ('Olivia', 'Smith', '2017-09-03', 'female', '<EMAIL>', '555-0305',
     '7410 Playground Drive, Kidstown, IL 62903', 'John Smith (Father): 555-0306',
     '{"provider": "Aetna", "policy_number": "AE852963741", "group_number": "GRP007", "member_id": "OS852963"}',
     '{"conditions": ["Type 1 Diabetes"], "allergies": ["NKDA"], "surgeries": [], "family_history": ["Grandfather: Type 1 DM"], "developmental": {"diabetes_management": "insulin pump", "immunizations": "up to date"}}')
  ) AS t(first_name, last_name, date_of_birth, gender, email, phone, address, emergency_contact, insurance_info, medical_history)
),
org_assignments AS (
  SELECT 
    pd.*,
    CASE 
      WHEN pd.first_name IN ('Sarah', 'Michael', 'Emily', 'Robert', 'Jessica') THEN 'Spritely Medical Center'
      WHEN pd.first_name IN ('David', 'Lisa', 'Kevin') THEN 'Spritely Community Clinic'  
      WHEN pd.first_name IN ('Emma', 'Jacob', 'Olivia') THEN 'Spritely Pediatrics'
    END as org_name
  FROM patient_demographics pd
)
INSERT INTO public.patients (
  id,
  organization_id,
  first_name,
  last_name,
  date_of_birth,
  gender,
  phone,
  email,
  address,
  emergency_contact,
  insurance_info,
  medical_history,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  oa.first_name,
  oa.last_name,
  oa.date_of_birth::date,
  oa.gender::gender,
  oa.phone,
  oa.email,
  oa.address,
  oa.emergency_contact,
  oa.insurance_info::jsonb,
  oa.medical_history::jsonb,
  NOW() - (random() * INTERVAL '2 years'),
  NOW() - (random() * INTERVAL '30 days')
FROM org_assignments oa
JOIN public.organizations o ON o.name = oa.org_name;

COMMIT;
