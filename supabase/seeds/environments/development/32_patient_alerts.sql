-- Start transaction
BEGIN;

-- Insert <PERSON>ient Alerts
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.patient_alerts (
  id,
  patient_id,
  alert_type,
  severity,
  description,
  start_date,
  end_date,
  created_by,
  status,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'allergy'
    WHEN '<PERSON>' THEN 'medication'
    WHEN '<PERSON>' THEN 'clinical'
    WHEN '<PERSON>' THEN 'administrative'
    WHEN '<PERSON>' THEN 'allergy'
  END as alert_type,
  CASE p.name
    WHEN '<PERSON>' THEN 'high'::alert_severity
    WHEN '<PERSON>' THEN 'medium'::alert_severity
    WHEN '<PERSON>' THEN 'medium'::alert_severity
    WHEN '<PERSON>' THEN 'low'::alert_severity
    WHEN '<PERSON>' THEN 'high'::alert_severity
  END as severity,
  CASE p.name
    WHEN '<PERSON>' THEN 'Penicillin allergy - anaphylaxis'
    WHEN '<PERSON>' THEN 'Monitor for side effects of new cardiac medication'
    WHEN '<PERSON>' THEN 'Asthma exacerbation risk during pollen season'
    WHEN 'David <PERSON>' THEN 'Insurance verification needed at next visit'
    WHEN 'Emma Garcia' THEN 'Egg allergy - avoid egg-based vaccines'
  END as description,
  NOW() - INTERVAL '6 months' as start_date,
  CASE p.name
    WHEN 'Alice Thompson' THEN NULL
    WHEN 'James Wilson' THEN NOW() + INTERVAL '3 months'
    WHEN 'Sophia Martinez' THEN NOW() + INTERVAL '2 months'
    WHEN 'David Anderson' THEN NOW() + INTERVAL '1 month'
    WHEN 'Emma Garcia' THEN NULL
  END as end_date,
  prov.user_id as created_by,
  'active'::alert_status as status,
  jsonb_build_object(
    'notes', CASE p.name
      WHEN 'Alice Thompson' THEN 'Patient experienced anaphylaxis with penicillin in 2010'
      WHEN 'James Wilson' THEN 'Started on new beta blocker, monitor for bradycardia'
      WHEN 'Sophia Martinez' THEN 'Increase monitoring during spring and fall'
      WHEN 'David Anderson' THEN 'Insurance card expired, need updated information'
      WHEN 'Emma Garcia' THEN 'Confirmed egg allergy, use alternative vaccines'
    END
  ) as metadata,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN prov.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN prov.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN prov.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN prov.name = 'Dr. Robert'
  END;

COMMIT;
