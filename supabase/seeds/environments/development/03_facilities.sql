-- Start transaction
BEGIN;

-- Insert Facilities using CTEs
WITH org_data (name, id) AS (
  SELECT name, id FROM organizations WHERE name IN ('Spritely Medical Center', 'Spritely Community Clinic', 'Spritely Pediatrics')
),
facility_data (name, id, org_name) AS (
  VALUES
    ('Spritely Main Hospital', gen_random_uuid(), 'Spritely Medical Center'),
    ('Spritely Community Clinic', gen_random_uuid(), 'Spritely Community Clinic'),
    ('Spritely Pediatric Center', gen_random_uuid(), 'Spritely Pediatrics')
)
INSERT INTO public.facilities (
  id,
  organization_id,
  name,
  type,
  address,
  contact_info,
  created_at,
  updated_at
)
SELECT
  f.id,
  o.id as organization_id,
  f.name,
  CASE f.name
    WHEN 'Spritely Main Hospital' THEN 'hospital'
    WHEN 'Spritely Community Clinic' THEN 'clinic'
    WHEN 'Spritely Pediatric Center' THEN 'specialty'
  END as type,
  CASE f.name
    WHEN 'Spritely Main Hospital' THEN '{"street": "123 Medical Way", "city": "Healthville", "state": "CA", "zip": "90210"}'::jsonb
    WHEN 'Spritely Community Clinic' THEN '{"street": "456 Wellness Blvd", "city": "Careville", "state": "CA", "zip": "90211"}'::jsonb
    WHEN 'Spritely Pediatric Center' THEN '{"street": "789 Children Way", "city": "Kidsville", "state": "CA", "zip": "90212"}'::jsonb
  END as address,
  CASE f.name
    WHEN 'Spritely Main Hospital' THEN '{"phone": "************", "email": "<EMAIL>"}'::jsonb
    WHEN 'Spritely Community Clinic' THEN '{"phone": "************", "email": "<EMAIL>"}'::jsonb
    WHEN 'Spritely Pediatric Center' THEN '{"phone": "************", "email": "<EMAIL>"}'::jsonb
  END as contact_info,
  NOW(),
  NOW()
FROM facility_data f
JOIN org_data o ON f.org_name = o.name;

-- Commit transaction
COMMIT;