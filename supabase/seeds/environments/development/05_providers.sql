-- Start transaction
BEGIN;

-- Insert Healthcare Providers using CTEs
WITH org_data AS (
  SELECT id, name FROM organizations
),
department_data AS (
  SELECT d.id, d.name, f.name as facility_name
  FROM departments d
  JOIN facilities f ON d.facility_id = f.id
),
user_data AS (
  SELECT
    id,
    raw_user_meta_data->>'name' as name,
    split_part(raw_user_meta_data->>'name', ' ', 1) as first_name,
    split_part(raw_user_meta_data->>'name', ' ', 2) as last_name,
    raw_user_meta_data->>'organization' as org_name,
    email
  FROM auth.users
  WHERE email LIKE 'physician%@%' OR email LIKE 'nurse%@%'
),
provider_data AS (
  SELECT
    gen_random_uuid() as id,
    name,
    first_name,
    last_name,
    CASE
      WHEN email LIKE '%1@spritelymedical%' THEN 'Primary Care'
      WHEN email LIKE '%2@spritelymedical%' THEN 'Cardiology'
      WHEN email LIKE '%3@spritelymedical%' THEN 'Neurology'
      WHEN email LIKE '%1@spritelyclinic%' THEN 'Primary Care'
      WHEN email LIKE '%2@spritelyclinic%' THEN 'Pediatrics'
      WHEN email LIKE '%1@spritelypediatrics%' THEN 'Pediatrics'
      WHEN email LIKE '%1@valleycardiology%' THEN 'Cardiology'
      WHEN email LIKE '%1@northsideregional%' THEN 'Surgery'
      ELSE 'Primary Care'
    END as department_name,
    CASE
      WHEN org_name = 'Spritely Medical Center' THEN 'Spritely Main Hospital'
      WHEN org_name = 'Spritely Community Clinic' THEN 'Spritely Community Clinic'
      WHEN org_name = 'Spritely Pediatrics' THEN 'Spritely Pediatric Center'
      WHEN org_name = 'Valley Cardiology Associates' THEN 'Valley Cardiology Center'
      WHEN org_name = 'Northside Regional Hospital' THEN 'Northside Regional Hospital'
      ELSE 'Main Facility'
    END as facility_name,
    org_name,
    id as user_id,
    email
  FROM user_data
)

-- Insert Healthcare Providers
INSERT INTO public.healthcare_providers (
  id,
  organization_id,
  department_id,
  user_id,
  first_name,
  last_name,
  credentials,
  specialties,
  provider_type,
  role,
  created_at,
  updated_at
)
SELECT
  p.id,
  o.id as organization_id,
  d.id as department_id,
  p.user_id,
  p.first_name,
  p.last_name,
  CASE
    WHEN p.email LIKE 'physician%@%' THEN '{"degree": "MD", "certifications": ["Board Certified"]}'::jsonb
    WHEN p.email LIKE 'nurse%@%' THEN '{"degree": "NP", "certifications": ["Registered Nurse"]}'::jsonb
    ELSE '{"degree": "MD", "certifications": ["Board Certified"]}'::jsonb
  END as credentials,
  CASE
    WHEN p.department_name = 'Primary Care' THEN ARRAY['family medicine', 'preventive care']
    WHEN p.department_name = 'Cardiology' THEN ARRAY['cardiology', 'interventional cardiology']
    WHEN p.department_name = 'Neurology' THEN ARRAY['neurology', 'stroke care']
    WHEN p.department_name = 'Pediatrics' THEN ARRAY['pediatrics', 'adolescent medicine']
    WHEN p.department_name = 'Surgery' THEN ARRAY['surgery', 'trauma']
    ELSE ARRAY['general medicine']
  END as specialties,
  CASE
    WHEN p.email LIKE 'nurse%@%' THEN 'nurse'::provider_type
    ELSE 'doctor'::provider_type
  END as provider_type,
  CASE
    WHEN p.email LIKE 'nurse%@%' THEN 'registered_nurse'::user_role
    ELSE 'physician'::user_role
  END as role,
  NOW() - (random() * interval '90 days'),
  NOW() - (random() * interval '30 days')
FROM provider_data p
JOIN org_data o ON p.org_name = o.name
LEFT JOIN department_data d ON p.department_name = d.name AND p.facility_name = d.facility_name
WHERE NOT EXISTS (
  SELECT 1 FROM public.healthcare_providers hp WHERE hp.user_id = p.user_id
);

COMMIT;