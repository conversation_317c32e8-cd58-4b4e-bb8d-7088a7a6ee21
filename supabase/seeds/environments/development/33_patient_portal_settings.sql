-- Start transaction
BEGIN;

-- Insert Patient Portal Settings
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
)
INSERT INTO public.patient_portal_settings (
  id,
  patient_id,
  preferences,
  communication_settings,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  jsonb_build_object(
    'portal_enabled', CASE p.name
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN false
    END,
    'last_login', CASE p.name
      WHEN '<PERSON>' THEN (NOW() - INTERVAL '2 days')::text
      WHEN '<PERSON>' THEN (NOW() - INTERVAL '1 week')::text
      WHEN '<PERSON>' THEN (NOW() - INTERVAL '3 days')::text
      WHEN '<PERSON>' THEN (NOW() - INTERVAL '1 month')::text
      WHEN '<PERSON>' THEN null
    END
  ) as preferences,
  jsonb_build_object(
    'email_notifications', CASE p.name
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN true
      WHEN '<PERSON>' THEN false
      WHEN '<PERSON>' THEN false
    END,
    'sms_notifications', CASE p.name
      WHEN 'Alice Thompson' THEN true
      WHEN 'James Wilson' THEN false
      WHEN 'Sophia Martinez' THEN true
      WHEN 'David Anderson' THEN true
      WHEN 'Emma Garcia' THEN false
    END
  ) as communication_settings,
  NOW(),
  NOW()
FROM patient_data p;

COMMIT;
