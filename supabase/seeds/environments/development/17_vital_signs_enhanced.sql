-- Enhanced Vital Signs with Realistic Physiological Data
BEGIN;

-- Create comprehensive vital signs with age-appropriate and condition-specific values
WITH patient_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.date_of_birth,
    p.gender,
    EXTRACT(YEAR FROM AGE(p.date_of_birth)) as age,
    p.medical_history,
    hp.id as recorded_by_provider_id
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  WHERE hp.provider_type IN ('doctor', 'nurse', 'specialist')
),
vital_signs_data AS (
  SELECT 
    pd.*,
    -- Generate realistic vital signs based on patient age and conditions
    CASE 
      -- Adult vital signs with condition modifications
      WHEN pd.age >= 18 THEN
        CASE 
          WHEN pd.patient_name = '<PERSON>' THEN -- <PERSON><PERSON><PERSON>, HTN
            jsonb_build_object(
              'systolic_bp', 150 + (random() * 20)::int,
              'diastolic_bp', 90 + (random() * 10)::int,
              'heart_rate', 75 + (random() * 15)::int,
              'respiratory_rate', 16 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.5 - 0.75),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 185 + (random() * 20)::int,
              'height', 165,
              'bmi', 33.2
            )
          WHEN pd.patient_name = 'Michael Chen' THEN -- CAD, Heart failure
            jsonb_build_object(
              'systolic_bp', 110 + (random() * 20)::int,
              'diastolic_bp', 70 + (random() * 10)::int,
              'heart_rate', 65 + (random() * 25)::int,
              'respiratory_rate', 18 + (random() * 6)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 94 + (random() * 4)::int,
              'weight', 175 + (random() * 15)::int,
              'height', 175,
              'bmi', 28.6
            )
          WHEN pd.patient_name = 'Emily Rodriguez' THEN -- Asthma, Anxiety
            jsonb_build_object(
              'systolic_bp', 115 + (random() * 15)::int,
              'diastolic_bp', 75 + (random() * 10)::int,
              'heart_rate', 85 + (random() * 20)::int,
              'respiratory_rate', 20 + (random() * 6)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 96 + (random() * 3)::int,
              'weight', 135 + (random() * 10)::int,
              'height', 162,
              'bmi', 25.1
            )
          WHEN pd.patient_name = 'Robert Williams' THEN -- COPD, Cancer
            jsonb_build_object(
              'systolic_bp', 125 + (random() * 20)::int,
              'diastolic_bp', 80 + (random() * 10)::int,
              'heart_rate', 90 + (random() * 20)::int,
              'respiratory_rate', 22 + (random() * 8)::int,
              'temperature', 98.6 + (random() * 1.5 - 0.75),
              'oxygen_saturation', 88 + (random() * 6)::int,
              'weight', 155 + (random() * 15)::int,
              'height', 178,
              'bmi', 24.5
            )
          WHEN pd.patient_name = 'Jessica Taylor' THEN -- Type 1 DM
            jsonb_build_object(
              'systolic_bp', 110 + (random() * 15)::int,
              'diastolic_bp', 70 + (random() * 10)::int,
              'heart_rate', 70 + (random() * 15)::int,
              'respiratory_rate', 16 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 98 + (random() * 2)::int,
              'weight', 125 + (random() * 10)::int,
              'height', 160,
              'bmi', 24.2
            )
          WHEN pd.patient_name = 'David Anderson' THEN -- HTN, Obesity
            jsonb_build_object(
              'systolic_bp', 160 + (random() * 25)::int,
              'diastolic_bp', 95 + (random() * 15)::int,
              'heart_rate', 80 + (random() * 20)::int,
              'respiratory_rate', 18 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 96 + (random() * 3)::int,
              'weight', 220 + (random() * 30)::int,
              'height', 175,
              'bmi', 36.0
            )
          WHEN pd.patient_name = 'Lisa Garcia' THEN -- Anemia
            jsonb_build_object(
              'systolic_bp', 105 + (random() * 15)::int,
              'diastolic_bp', 65 + (random() * 10)::int,
              'heart_rate', 90 + (random() * 20)::int,
              'respiratory_rate', 16 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 140 + (random() * 15)::int,
              'height', 163,
              'bmi', 26.4
            )
          WHEN pd.patient_name = 'Kevin Lee' THEN -- Back pain
            jsonb_build_object(
              'systolic_bp', 125 + (random() * 15)::int,
              'diastolic_bp', 80 + (random() * 10)::int,
              'heart_rate', 75 + (random() * 15)::int,
              'respiratory_rate', 16 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 98 + (random() * 2)::int,
              'weight', 180 + (random() * 20)::int,
              'height', 180,
              'bmi', 27.8
            )
          ELSE -- Default adult values
            jsonb_build_object(
              'systolic_bp', 120 + (random() * 20)::int,
              'diastolic_bp', 80 + (random() * 10)::int,
              'heart_rate', 70 + (random() * 20)::int,
              'respiratory_rate', 16 + (random() * 4)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 150 + (random() * 30)::int,
              'height', 170,
              'bmi', 26.0
            )
        END
      -- Pediatric vital signs (age-appropriate)
      ELSE
        CASE 
          WHEN pd.age <= 2 THEN -- Toddler
            jsonb_build_object(
              'systolic_bp', 85 + (random() * 15)::int,
              'diastolic_bp', 50 + (random() * 10)::int,
              'heart_rate', 100 + (random() * 40)::int,
              'respiratory_rate', 25 + (random() * 10)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 12 + (random() * 3)::int,
              'height', 85 + (random() * 10)::int,
              'head_circumference', 47 + (random() * 3)::int
            )
          WHEN pd.age <= 5 THEN -- Preschooler
            jsonb_build_object(
              'systolic_bp', 95 + (random() * 15)::int,
              'diastolic_bp', 55 + (random() * 10)::int,
              'heart_rate', 90 + (random() * 30)::int,
              'respiratory_rate', 22 + (random() * 8)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 18 + (random() * 4)::int,
              'height', 105 + (random() * 10)::int
            )
          ELSE -- School age
            jsonb_build_object(
              'systolic_bp', 100 + (random() * 15)::int,
              'diastolic_bp', 60 + (random() * 10)::int,
              'heart_rate', 80 + (random() * 25)::int,
              'respiratory_rate', 20 + (random() * 6)::int,
              'temperature', 98.6 + (random() * 1.0 - 0.5),
              'oxygen_saturation', 97 + (random() * 3)::int,
              'weight', 22 + (random() * 8)::int,
              'height', 115 + (random() * 15)::int
            )
        END
    END as vital_signs,
    -- Generate multiple time points for trending
    generate_series(1, 3) as visit_number
  FROM patient_data pd
)
INSERT INTO public.vital_signs (
  id,
  organization_id,
  patient_id,
  recorded_by,
  recorded_at,
  systolic_bp,
  diastolic_bp,
  heart_rate,
  respiratory_rate,
  temperature,
  oxygen_saturation,
  weight,
  height,
  bmi,
  pain_score,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  vsd.organization_id,
  vsd.patient_id,
  vsd.recorded_by_provider_id,
  NOW() - (vsd.visit_number * INTERVAL '30 days') - (random() * INTERVAL '7 days'),
  (vsd.vital_signs->>'systolic_bp')::integer,
  (vsd.vital_signs->>'diastolic_bp')::integer,
  (vsd.vital_signs->>'heart_rate')::integer,
  (vsd.vital_signs->>'respiratory_rate')::integer,
  (vsd.vital_signs->>'temperature')::numeric,
  (vsd.vital_signs->>'oxygen_saturation')::integer,
  (vsd.vital_signs->>'weight')::numeric,
  (vsd.vital_signs->>'height')::numeric,
  (vsd.vital_signs->>'bmi')::numeric,
  CASE 
    WHEN vsd.patient_name = 'Kevin Lee' THEN 6 + (random() * 4)::int -- Back pain
    WHEN vsd.patient_name = 'Robert Williams' THEN 4 + (random() * 3)::int -- Cancer pain
    WHEN vsd.age < 18 THEN NULL -- Pediatric patients
    ELSE (random() * 3)::int -- Minimal pain for others
  END,
  CASE 
    WHEN vsd.patient_name = 'Michael Chen' THEN 'Patient appears short of breath. Bilateral lower extremity edema noted.'
    WHEN vsd.patient_name = 'Emily Rodriguez' THEN 'Mild wheezing on expiration. Using accessory muscles.'
    WHEN vsd.patient_name = 'Robert Williams' THEN 'Oxygen saturation low. Patient on supplemental O2.'
    WHEN vsd.patient_name = 'David Anderson' THEN 'Blood pressure elevated. Patient reports medication non-adherence.'
    WHEN vsd.age < 18 THEN 'Pediatric vital signs within normal limits for age.'
    ELSE 'Vital signs stable. Patient comfortable.'
  END,
  NOW() - (vsd.visit_number * INTERVAL '30 days') - (random() * INTERVAL '7 days'),
  NOW() - (vsd.visit_number * INTERVAL '30 days') - (random() * INTERVAL '5 days')
FROM vital_signs_data vsd;

COMMIT;
