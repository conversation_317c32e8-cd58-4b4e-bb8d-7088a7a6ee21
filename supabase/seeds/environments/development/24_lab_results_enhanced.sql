-- Enhanced Lab Results with Realistic Medical Laboratory Values
BEGIN;

-- Create comprehensive lab results with proper reference ranges and clinical correlation
WITH patient_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.date_of_birth,
    p.gender,
    hp.id as ordering_provider_id
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  WHERE hp.provider_type IN ('doctor', 'specialist')
),
lab_test_data AS (
  SELECT * FROM (VALUES
    -- Complete Blood Count (CBC) with Differential
    ('Complete Blood Count', 'CBC', 'Hematology',
     '{"tests": [
       {"name": "White Blood Cell Count", "value": "7.2", "unit": "K/uL", "reference_range": "4.0-11.0", "status": "normal"},
       {"name": "Red Blood Cell Count", "value": "4.5", "unit": "M/uL", "reference_range": "4.2-5.4", "status": "normal"},
       {"name": "Hemoglobin", "value": "14.2", "unit": "g/dL", "reference_range": "12.0-16.0", "status": "normal"},
       {"name": "Hematocrit", "value": "42.1", "unit": "%", "reference_range": "36.0-46.0", "status": "normal"},
       {"name": "Platelet Count", "value": "285", "unit": "K/uL", "reference_range": "150-450", "status": "normal"},
       {"name": "Neutrophils", "value": "65", "unit": "%", "reference_range": "50-70", "status": "normal"},
       {"name": "Lymphocytes", "value": "28", "unit": "%", "reference_range": "20-40", "status": "normal"}
     ]}'),
    
    -- Comprehensive Metabolic Panel (CMP)
    ('Comprehensive Metabolic Panel', 'CMP', 'Chemistry',
     '{"tests": [
       {"name": "Glucose", "value": "95", "unit": "mg/dL", "reference_range": "70-100", "status": "normal"},
       {"name": "BUN", "value": "18", "unit": "mg/dL", "reference_range": "7-20", "status": "normal"},
       {"name": "Creatinine", "value": "1.0", "unit": "mg/dL", "reference_range": "0.6-1.2", "status": "normal"},
       {"name": "eGFR", "value": ">60", "unit": "mL/min/1.73m²", "reference_range": ">60", "status": "normal"},
       {"name": "Sodium", "value": "140", "unit": "mmol/L", "reference_range": "136-145", "status": "normal"},
       {"name": "Potassium", "value": "4.2", "unit": "mmol/L", "reference_range": "3.5-5.1", "status": "normal"},
       {"name": "Chloride", "value": "102", "unit": "mmol/L", "reference_range": "98-107", "status": "normal"},
       {"name": "CO2", "value": "24", "unit": "mmol/L", "reference_range": "22-28", "status": "normal"}
     ]}'),
    
    -- Lipid Panel
    ('Lipid Panel', 'LIPID', 'Chemistry',
     '{"tests": [
       {"name": "Total Cholesterol", "value": "195", "unit": "mg/dL", "reference_range": "<200", "status": "normal"},
       {"name": "HDL Cholesterol", "value": "55", "unit": "mg/dL", "reference_range": ">40", "status": "normal"},
       {"name": "LDL Cholesterol", "value": "115", "unit": "mg/dL", "reference_range": "<100", "status": "borderline"},
       {"name": "Triglycerides", "value": "125", "unit": "mg/dL", "reference_range": "<150", "status": "normal"}
     ]}'),
    
    -- Hemoglobin A1C
    ('Hemoglobin A1C', 'HBA1C', 'Chemistry',
     '{"tests": [
       {"name": "Hemoglobin A1C", "value": "6.8", "unit": "%", "reference_range": "<7.0", "status": "borderline"},
       {"name": "Estimated Average Glucose", "value": "148", "unit": "mg/dL", "reference_range": "N/A", "status": "calculated"}
     ]}'),
    
    -- Thyroid Function Tests
    ('Thyroid Function Panel', 'THYROID', 'Chemistry',
     '{"tests": [
       {"name": "TSH", "value": "2.5", "unit": "mIU/L", "reference_range": "0.4-4.0", "status": "normal"},
       {"name": "Free T4", "value": "1.2", "unit": "ng/dL", "reference_range": "0.8-1.8", "status": "normal"},
       {"name": "Free T3", "value": "3.1", "unit": "pg/mL", "reference_range": "2.3-4.2", "status": "normal"}
     ]}'),
    
    -- Liver Function Tests
    ('Liver Function Panel', 'LFT', 'Chemistry',
     '{"tests": [
       {"name": "ALT", "value": "28", "unit": "U/L", "reference_range": "7-56", "status": "normal"},
       {"name": "AST", "value": "32", "unit": "U/L", "reference_range": "10-40", "status": "normal"},
       {"name": "Alkaline Phosphatase", "value": "85", "unit": "U/L", "reference_range": "44-147", "status": "normal"},
       {"name": "Total Bilirubin", "value": "0.8", "unit": "mg/dL", "reference_range": "0.3-1.2", "status": "normal"},
       {"name": "Albumin", "value": "4.2", "unit": "g/dL", "reference_range": "3.5-5.0", "status": "normal"}
     ]}'),
    
    -- Urinalysis
    ('Urinalysis', 'UA', 'Urinalysis',
     '{"tests": [
       {"name": "Color", "value": "Yellow", "unit": "", "reference_range": "Yellow", "status": "normal"},
       {"name": "Clarity", "value": "Clear", "unit": "", "reference_range": "Clear", "status": "normal"},
       {"name": "Specific Gravity", "value": "1.020", "unit": "", "reference_range": "1.003-1.030", "status": "normal"},
       {"name": "pH", "value": "6.0", "unit": "", "reference_range": "5.0-8.0", "status": "normal"},
       {"name": "Protein", "value": "Negative", "unit": "", "reference_range": "Negative", "status": "normal"},
       {"name": "Glucose", "value": "Negative", "unit": "", "reference_range": "Negative", "status": "normal"},
       {"name": "Ketones", "value": "Negative", "unit": "", "reference_range": "Negative", "status": "normal"},
       {"name": "Blood", "value": "Negative", "unit": "", "reference_range": "Negative", "status": "normal"}
     ]}'),
    
    -- Coagulation Studies
    ('Coagulation Panel', 'COAG', 'Hematology',
     '{"tests": [
       {"name": "PT", "value": "12.5", "unit": "seconds", "reference_range": "11.0-13.0", "status": "normal"},
       {"name": "INR", "value": "1.0", "unit": "", "reference_range": "0.8-1.2", "status": "normal"},
       {"name": "PTT", "value": "28", "unit": "seconds", "reference_range": "25-35", "status": "normal"}
     ]}'),
    
    -- Cardiac Markers
    ('Cardiac Markers', 'CARDIAC', 'Chemistry',
     '{"tests": [
       {"name": "Troponin I", "value": "<0.04", "unit": "ng/mL", "reference_range": "<0.04", "status": "normal"},
       {"name": "CK-MB", "value": "2.5", "unit": "ng/mL", "reference_range": "0.0-6.3", "status": "normal"},
       {"name": "BNP", "value": "125", "unit": "pg/mL", "reference_range": "<100", "status": "borderline"}
     ]}'),
    
    -- Inflammatory Markers
    ('Inflammatory Markers', 'INFLAM', 'Chemistry',
     '{"tests": [
       {"name": "ESR", "value": "15", "unit": "mm/hr", "reference_range": "0-30", "status": "normal"},
       {"name": "CRP", "value": "2.1", "unit": "mg/L", "reference_range": "<3.0", "status": "normal"},
       {"name": "Procalcitonin", "value": "0.08", "unit": "ng/mL", "reference_range": "<0.25", "status": "normal"}
     ]}')
  ) AS t(test_name, test_code, category, results_json)
)
INSERT INTO public.lab_results (
  id,
  organization_id,
  patient_id,
  ordering_provider_id,
  test_name,
  test_code,
  category,
  results,
  collected_at,
  resulted_at,
  status,
  reference_ranges,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pd.organization_id,
  pd.patient_id,
  pd.ordering_provider_id,
  ltd.test_name,
  ltd.test_code,
  ltd.category,
  -- Modify results based on patient conditions for realism
  CASE 
    WHEN pd.patient_name = 'Sarah Johnson' AND ltd.test_code = 'HBA1C' THEN 
      '{"tests": [{"name": "Hemoglobin A1C", "value": "8.2", "unit": "%", "reference_range": "<7.0", "status": "high"}]}'::jsonb
    WHEN pd.patient_name = 'Michael Chen' AND ltd.test_code = 'CARDIAC' THEN
      '{"tests": [{"name": "BNP", "value": "850", "unit": "pg/mL", "reference_range": "<100", "status": "high"}]}'::jsonb
    WHEN pd.patient_name = 'Lisa Garcia' AND ltd.test_code = 'CBC' THEN
      '{"tests": [{"name": "Hemoglobin", "value": "9.2", "unit": "g/dL", "reference_range": "12.0-16.0", "status": "low"}]}'::jsonb
    ELSE ltd.results_json::jsonb
  END as results,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '25 days'),
  'final',
  '{"normal_ranges": "Age and gender specific"}',
  CASE 
    WHEN ltd.test_code = 'HBA1C' THEN 'Diabetes monitoring - target <7% for most adults'
    WHEN ltd.test_code = 'LIPID' THEN 'Cardiovascular risk assessment'
    WHEN ltd.test_code = 'CBC' THEN 'Routine screening and monitoring'
    ELSE 'Clinical correlation recommended'
  END,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '20 days')
FROM patient_data pd
CROSS JOIN lab_test_data ltd
WHERE random() < 0.4  -- Create labs for 40% of patient-test combinations
ORDER BY random();

COMMIT;
