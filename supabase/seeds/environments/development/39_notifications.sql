-- Start transaction
BEGIN;

-- Skip notification preferences as they're already created in auth.sql

-- Insert Notifications
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name, organization_id, user_id
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name, user_id, organization_id
  FROM public.healthcare_providers
),
notification_template_data AS (
  SELECT id, name, type, organization_id
  FROM public.notification_templates
)
INSERT INTO public.notifications (
  id,
  organization_id,
  recipient_id,
  type,
  priority,
  status,
  title,
  content,
  metadata,
  created_at,
  updated_at
)
-- Patient notifications
SELECT
  uuid_generate_v4(),
  p.organization_id,
  CASE p.name
    WHEN '<PERSON>' THEN '11111111-1111-1111-1111-111111111111'::uuid
    WHEN '<PERSON>' THEN '*************-2222-2222-************'::uuid
    WHEN '<PERSON>' THEN '*************-3333-3333-************'::uuid
    WHEN '<PERSON>' THEN '*************-4444-4444-************'::uuid
    WHEN '<PERSON>' THEN '*************-5555-5555-************'::uuid
  END as recipient_id,
  nt.type,
  'medium'::notification_priority as priority,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'read'::notification_status
    WHEN 'James Wilson' THEN 'delivered'::notification_status
    WHEN 'Sophia <PERSON>' THEN 'read'::notification_status
    WHEN 'David Anderson' THEN 'delivered'::notification_status
    WHEN 'Emma Garcia' THEN 'delivered'::notification_status
  END as status,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Appointment Reminder'
    WHEN 'James Wilson' THEN 'Lab Results Available'
    WHEN 'Sophia Martinez' THEN 'Prescription Refill Ready'
    WHEN 'David Anderson' THEN 'Appointment Confirmation'
    WHEN 'Emma Garcia' THEN 'Vaccination Reminder'
  END as title,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'You have an appointment with Dr. Smith tomorrow at 10:00 AM.'
    WHEN 'James Wilson' THEN 'Your recent lab results are now available. Please log in to your patient portal to view them.'
    WHEN 'Sophia Martinez' THEN 'Your prescription for Albuterol is ready for pickup at your pharmacy.'
    WHEN 'David Anderson' THEN 'Your appointment with Dr. Davis has been scheduled for next Monday at 2:00 PM.'
    WHEN 'Emma Garcia' THEN 'Emma is due for her next set of vaccinations. Please call our office to schedule an appointment.'
  END as content,
  jsonb_build_object(
    'patient_id', p.id,
    'sent_via', CASE p.name
      WHEN 'Alice Thompson' THEN '["email", "sms"]'
      WHEN 'James Wilson' THEN '["email"]'
      WHEN 'Sophia Martinez' THEN '["email", "sms"]'
      WHEN 'David Anderson' THEN '["email"]'
      WHEN 'Emma Garcia' THEN '["email"]'
    END
  ) as metadata,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() - INTERVAL '1 day'
    WHEN 'James Wilson' THEN NOW() - INTERVAL '2 days'
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '3 days'
    WHEN 'David Anderson' THEN NOW() - INTERVAL '4 days'
    WHEN 'Emma Garcia' THEN NOW() - INTERVAL '5 days'
  END as created_at,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() - INTERVAL '1 day'
    WHEN 'James Wilson' THEN NOW() - INTERVAL '2 days'
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '3 days'
    WHEN 'David Anderson' THEN NOW() - INTERVAL '4 days'
    WHEN 'Emma Garcia' THEN NOW() - INTERVAL '5 days'
  END as updated_at
FROM patient_data p
JOIN notification_template_data nt ON p.organization_id = nt.organization_id AND
  CASE p.name
    WHEN 'Alice Thompson' THEN nt.name = 'Appointment Reminder'
    WHEN 'James Wilson' THEN nt.name = 'Lab Results Available'
    WHEN 'Sophia Martinez' THEN nt.name = 'Prescription Refill'
    WHEN 'David Anderson' THEN nt.name = 'Appointment Confirmation'
    WHEN 'Emma Garcia' THEN nt.name = 'Vaccination Due'
  END
WHERE p.name IN ('Alice Thompson', 'James Wilson', 'Sophia Martinez', 'David Anderson', 'Emma Garcia')

UNION ALL

-- Provider notifications
SELECT
  uuid_generate_v4(),
  prov.organization_id,
  prov.user_id as recipient_id,
  'task_assignment'::notification_type as type,
  'medium'::notification_priority as priority,
  CASE prov.name
    WHEN 'Dr. Sarah' THEN 'read'::notification_status
    WHEN 'Dr. Michael' THEN 'delivered'::notification_status
    WHEN 'Dr. Lisa' THEN 'read'::notification_status
    WHEN 'Dr. James' THEN 'delivered'::notification_status
    WHEN 'Dr. Robert' THEN 'delivered'::notification_status
  END as status,
  CASE prov.name
    WHEN 'Dr. Sarah' THEN 'New Task Assigned'
    WHEN 'Dr. Michael' THEN 'New Task Assigned'
    WHEN 'Dr. Lisa' THEN 'New Task Assigned'
    WHEN 'Dr. James' THEN 'New Task Assigned'
    WHEN 'Dr. Robert' THEN 'New Task Assigned'
  END as title,
  CASE prov.name
    WHEN 'Dr. Sarah' THEN 'You have been assigned a new task: Follow up with Alice Thompson'
    WHEN 'Dr. Michael' THEN 'You have been assigned a new task: Review James Wilson''s echocardiogram'
    WHEN 'Dr. Lisa' THEN 'You have been assigned a new task: Update Sophia Martinez''s vaccination record'
    WHEN 'Dr. James' THEN 'You have been assigned a new task: Call pharmacy for David Anderson'
    WHEN 'Dr. Robert' THEN 'You have been assigned a new task: Schedule follow-up for Emma Garcia'
  END as content,
  jsonb_build_object(
    'patient_id', p.id,
    'sent_via', '["email"]'
  ) as metadata,
  NOW() - INTERVAL '1 week' as created_at,
  NOW() - INTERVAL '1 week' as updated_at
FROM provider_data prov
JOIN patient_data p ON
  CASE prov.name
    WHEN 'Dr. Sarah' THEN p.name = 'Alice Thompson'
    WHEN 'Dr. Michael' THEN p.name = 'James Wilson'
    WHEN 'Dr. Lisa' THEN p.name = 'Sophia Martinez'
    WHEN 'Dr. James' THEN p.name = 'David Anderson'
    WHEN 'Dr. Robert' THEN p.name = 'Emma Garcia'
  END
WHERE prov.name IN ('Dr. Sarah', 'Dr. Michael', 'Dr. Lisa', 'Dr. James', 'Dr. Robert');

COMMIT;
