-- Enhanced Claims with Realistic Medical Billing and Insurance Processing
BEGIN;

-- Create comprehensive insurance claims with proper medical billing workflows
WITH patient_appointment_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.insurance_info,
    a.id as appointment_id,
    a.provider_id,
    a.appointment_date,
    a.reason,
    a.duration_minutes,
    hp.first_name || ' ' || hp.last_name as provider_name,
    hp.npi_number
  FROM public.patients p
  JOIN public.appointments a ON a.patient_id = p.id
  JOIN public.healthcare_providers hp ON hp.id = a.provider_id
  WHERE a.status = 'completed'
),
claim_data AS (
  SELECT * FROM (VALUES
    -- Primary Care Claims
    ('<PERSON>', 'Diabetes follow-up', '99214', 'E11.9', 150.00, 120.00, 30.00, 'Blue Cross Blue Shield', 'BC123456789'),
    ('<PERSON>', 'HbA1c test', '83036', 'E11.9', 85.00, 68.00, 17.00, 'Blue Cross Blue Shield', 'BC123456789'),
    ('<PERSON>', 'Lipid panel', '80061', 'E78.5', 95.00, 76.00, 19.00, '<PERSON> Cross Blue Shield', 'BC123456789'),
    
    ('<PERSON>', 'Cardiology follow-up', '99214', 'I50.9', 200.00, 160.00, 40.00, 'Aetna', 'AE987654321'),
    ('<PERSON> Chen', 'Echocardiogram', '93306', 'I50.9', 850.00, 680.00, 170.00, 'Aetna', 'AE987654321'),
    ('Michael Chen', 'BNP test', '83880', 'I50.9', 125.00, 100.00, 25.00, 'Aetna', 'AE987654321'),
    ('Michael Chen', 'INR monitoring', '85610', 'I48.91', 45.00, 36.00, 9.00, 'Aetna', 'AE987654321'),
    
    ('Emily Rodriguez', 'Asthma follow-up', '99213', 'J45.9', 125.00, 100.00, 25.00, 'United Healthcare', 'UH456789123'),
    ('Emily Rodriguez', 'Pulmonary function test', '94010', 'J45.9', 275.00, 220.00, 55.00, 'United Healthcare', 'UH456789123'),
    ('Emily Rodriguez', 'Chest X-ray', '71045', 'J45.9', 180.00, 144.00, 36.00, 'United Healthcare', 'UH456789123'),
    
    ('Robert Williams', 'Oncology follow-up', '99215', 'C78.01', 300.00, 240.00, 60.00, 'Medicare + Supplement', 'MC789123456'),
    ('Robert Williams', 'CT chest scan', '71250', 'C78.01', 1200.00, 960.00, 240.00, 'Medicare + Supplement', 'MC789123456'),
    ('Robert Williams', 'CBC with differential', '85025', 'C78.01', 65.00, 52.00, 13.00, 'Medicare + Supplement', 'MC789123456'),
    
    ('Jessica Taylor', 'Endocrinology visit', '99214', 'E10.9', 175.00, 140.00, 35.00, 'Cigna', 'CI321654987'),
    ('Jessica Taylor', 'HbA1c test', '83036', 'E10.9', 85.00, 68.00, 17.00, 'Cigna', 'CI321654987'),
    ('Jessica Taylor', 'Microalbumin test', '82043', 'E10.9', 95.00, 76.00, 19.00, 'Cigna', 'CI321654987'),
    
    ('David Anderson', 'Annual physical', '99396', 'Z00.00', 250.00, 200.00, 50.00, 'Medicaid', 'MD135792468'),
    ('David Anderson', 'Blood pressure check', '99211', 'I10', 75.00, 60.00, 15.00, 'Medicaid', 'MD135792468'),
    ('David Anderson', 'Comprehensive metabolic panel', '80053', 'I10', 110.00, 88.00, 22.00, 'Medicaid', 'MD135792468'),
    
    ('Lisa Garcia', 'Postpartum visit', '99213', 'Z39.2', 150.00, 120.00, 30.00, 'Humana', 'HU864209753'),
    ('Lisa Garcia', 'CBC test', '85025', 'D50.9', 65.00, 52.00, 13.00, 'Humana', 'HU864209753'),
    ('Lisa Garcia', 'Iron studies', '83540', 'D50.9', 125.00, 100.00, 25.00, 'Humana', 'HU864209753'),
    
    ('Kevin Lee', 'Pain management visit', '99214', 'M54.5', 175.00, 140.00, 35.00, 'Blue Cross', 'BC975318642'),
    ('Kevin Lee', 'MRI lumbar spine', '72148', 'M54.5', 2200.00, 1760.00, 440.00, 'Blue Cross', 'BC975318642'),
    ('Kevin Lee', 'Physical therapy evaluation', '97161', 'M54.5', 185.00, 148.00, 37.00, 'Blue Cross', 'BC975318642'),
    
    -- Pediatric Claims
    ('Emma Garcia', 'Well child visit', '99382', 'Z00.121', 200.00, 160.00, 40.00, 'CHIP', 'CH147258369'),
    ('Emma Garcia', 'Immunizations', '90471', 'Z23', 45.00, 36.00, 9.00, 'CHIP', 'CH147258369'),
    ('Emma Garcia', 'Allergy testing', '95004', 'T78.1', 350.00, 280.00, 70.00, 'CHIP', 'CH147258369'),
    
    ('Jacob Martinez', 'ADHD follow-up', '99213', 'F90.9', 150.00, 120.00, 30.00, 'Medicaid', 'MD963741852'),
    ('Jacob Martinez', 'Speech therapy', '92507', 'F80.9', 125.00, 100.00, 25.00, 'Medicaid', 'MD963741852'),
    ('Jacob Martinez', 'Developmental assessment', '96116', 'F90.9', 275.00, 220.00, 55.00, 'Medicaid', 'MD963741852'),
    
    ('Olivia Smith', 'Diabetes follow-up', '99213', 'E10.9', 150.00, 120.00, 30.00, 'Aetna', 'AE852963741'),
    ('Olivia Smith', 'HbA1c test', '83036', 'E10.9', 85.00, 68.00, 17.00, 'Aetna', 'AE852963741'),
    ('Olivia Smith', 'Diabetes supplies', 'A4253', 'E10.9', 185.00, 148.00, 37.00, 'Aetna', 'AE852963741')
  ) AS t(patient_name, service_description, procedure_code, diagnosis_code, billed_amount, allowed_amount, patient_responsibility, insurance_name, policy_number)
)
INSERT INTO public.claims (
  id,
  patient_id,
  provider_id,
  appointment_id,
  claim_number,
  service_date,
  procedure_codes,
  diagnosis_codes,
  billed_amount,
  allowed_amount,
  paid_amount,
  patient_responsibility,
  insurance_info,
  status,
  submitted_date,
  processed_date,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pad.patient_id,
  pad.provider_id,
  pad.appointment_id,
  'CLM' || to_char(NOW(), 'YYYYMMDD') || '-' || LPAD((row_number() OVER ())::text, 6, '0'),
  pad.appointment_date::date,
  ARRAY[cd.procedure_code],
  ARRAY[cd.diagnosis_code],
  cd.billed_amount,
  cd.allowed_amount,
  CASE 
    WHEN random() < 0.8 THEN cd.allowed_amount  -- 80% fully paid
    WHEN random() < 0.9 THEN cd.allowed_amount * 0.8  -- 10% partial payment
    ELSE 0  -- 10% denied
  END as paid_amount,
  cd.patient_responsibility,
  jsonb_build_object(
    'insurance_name', cd.insurance_name,
    'policy_number', cd.policy_number,
    'group_number', (pad.insurance_info->>'group_number'),
    'member_id', (pad.insurance_info->>'member_id')
  ),
  CASE 
    WHEN random() < 0.7 THEN 'paid'::claim_status
    WHEN random() < 0.85 THEN 'pending'::claim_status
    WHEN random() < 0.95 THEN 'processing'::claim_status
    ELSE 'denied'::claim_status
  END as status,
  pad.appointment_date + INTERVAL '1 day',
  CASE 
    WHEN random() < 0.8 THEN pad.appointment_date + INTERVAL '14 days'
    ELSE NULL  -- Pending claims not yet processed
  END as processed_date,
  CASE 
    WHEN random() < 0.1 THEN 'Prior authorization required'
    WHEN random() < 0.15 THEN 'Additional documentation requested'
    WHEN random() < 0.2 THEN 'Coordination of benefits required'
    ELSE 'Standard processing'
  END as notes,
  pad.appointment_date + INTERVAL '1 day',
  CASE 
    WHEN random() < 0.8 THEN pad.appointment_date + INTERVAL '15 days'
    ELSE NOW()
  END as updated_at
FROM patient_appointment_data pad
JOIN claim_data cd ON cd.patient_name = pad.patient_name 
  AND (cd.service_description ILIKE '%' || split_part(pad.reason, ' ', 1) || '%' 
       OR cd.service_description ILIKE '%follow-up%' AND pad.reason ILIKE '%follow-up%'
       OR cd.service_description ILIKE '%physical%' AND pad.reason ILIKE '%physical%')
WHERE random() < 0.8  -- Create claims for 80% of completed appointments
ORDER BY pad.appointment_date;

-- Create additional claims for lab tests and procedures
INSERT INTO public.claims (
  id,
  patient_id,
  provider_id,
  claim_number,
  service_date,
  procedure_codes,
  diagnosis_codes,
  billed_amount,
  allowed_amount,
  paid_amount,
  patient_responsibility,
  insurance_info,
  status,
  submitted_date,
  processed_date,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pad.patient_id,
  pad.provider_id,
  'CLM' || to_char(NOW(), 'YYYYMMDD') || '-' || LPAD((1000 + row_number() OVER ())::text, 6, '0'),
  pad.appointment_date::date + INTERVAL '1 day',
  ARRAY[cd.procedure_code],
  ARRAY[cd.diagnosis_code],
  cd.billed_amount,
  cd.allowed_amount,
  cd.allowed_amount * 0.9,  -- Most lab claims paid at 90%
  cd.patient_responsibility,
  jsonb_build_object(
    'insurance_name', cd.insurance_name,
    'policy_number', cd.policy_number
  ),
  'paid'::claim_status,
  pad.appointment_date + INTERVAL '2 days',
  pad.appointment_date + INTERVAL '10 days',
  'Laboratory services claim',
  pad.appointment_date + INTERVAL '2 days',
  pad.appointment_date + INTERVAL '11 days'
FROM patient_appointment_data pad
JOIN claim_data cd ON cd.patient_name = pad.patient_name 
WHERE cd.procedure_code IN ('83036', '80053', '80061', '85025', '83880', '85610', '82043', '83540')
  AND random() < 0.6;  -- Create lab claims for 60% of patients

COMMIT;
