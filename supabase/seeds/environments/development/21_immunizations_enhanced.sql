-- Enhanced Immunizations with CDC-Recommended Vaccination Schedules
BEGIN;

-- Create comprehensive immunization records following CDC guidelines
WITH patient_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.date_of_birth,
    p.gender,
    EXTRACT(YEAR FROM AGE(p.date_of_birth)) as age,
    hp.id as administering_provider_id
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  WHERE hp.provider_type IN ('doctor', 'nurse')
),
immunization_data AS (
  SELECT * FROM (VALUES
    -- Adult Immunizations
    ('<PERSON>', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'San<PERSON>i Pasteur', 
     'intramuscular', 'left_arm', 1, '2023-10-15', '2024-10-15', 'FL2023A', 'Annual influenza vaccination'),
    ('<PERSON>', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2021-05-20', '2031-05-20', 'TD2021B', 'Tdap booster every 10 years'),
    ('<PERSON>', 'Zoster vaccine', '90736', 'Shingrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2023-03-10', NULL, 'ZV2023A', 'First dose of 2-dose series'),
    ('Sarah Johnson', 'Zoster vaccine', '90736', 'Shingrix', 'GlaxoSmithKline',
     'intramuscular', 'right_arm', 2, '2023-05-10', NULL, 'ZV2023B', 'Second dose of 2-dose series'),
    
    ('Michael Chen', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-09-25', '2024-09-25', 'FL2023C', 'Annual influenza vaccination'),
    ('Michael Chen', 'Pneumococcal vaccine', '90732', 'Pneumovax 23', 'Merck',
     'intramuscular', 'left_arm', 1, '2022-08-15', '2027-08-15', 'PV2022A', 'Pneumococcal vaccination for cardiac patient'),
    ('Michael Chen', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'right_arm', 1, '2020-11-12', '2030-11-12', 'TD2020C', 'Tdap booster'),
    
    ('Emily Rodriguez', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-10-05', '2024-10-05', 'FL2023D', 'Annual influenza vaccination'),
    ('Emily Rodriguez', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2022-07-18', '2032-07-18', 'TD2022D', 'Tdap booster'),
    ('Emily Rodriguez', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 1, '2021-01-15', NULL, 'HPV2021A', 'First dose of 3-dose series'),
    ('Emily Rodriguez', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'right_arm', 2, '2021-03-15', NULL, 'HPV2021B', 'Second dose of 3-dose series'),
    ('Emily Rodriguez', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 3, '2021-09-15', NULL, 'HPV2021C', 'Third dose of 3-dose series'),
    
    ('Robert Williams', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone High-Dose', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-09-20', '2024-09-20', 'FL2023E', 'High-dose flu vaccine for older adult'),
    ('Robert Williams', 'Pneumococcal vaccine', '90732', 'Pneumovax 23', 'Merck',
     'intramuscular', 'left_arm', 1, '2021-06-10', '2026-06-10', 'PV2021B', 'Pneumococcal vaccination for COPD patient'),
    ('Robert Williams', 'Zoster vaccine', '90736', 'Shingrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2022-04-12', NULL, 'ZV2022A', 'First dose of 2-dose series'),
    ('Robert Williams', 'Zoster vaccine', '90736', 'Shingrix', 'GlaxoSmithKline',
     'intramuscular', 'right_arm', 2, '2022-06-12', NULL, 'ZV2022B', 'Second dose of 2-dose series'),
    
    ('Jessica Taylor', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-10-12', '2024-10-12', 'FL2023F', 'Annual influenza vaccination'),
    ('Jessica Taylor', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2023-02-28', '2033-02-28', 'TD2023E', 'Tdap booster'),
    ('Jessica Taylor', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 1, '2020-06-15', NULL, 'HPV2020A', 'First dose of 3-dose series'),
    ('Jessica Taylor', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'right_arm', 2, '2020-08-15', NULL, 'HPV2020B', 'Second dose of 3-dose series'),
    ('Jessica Taylor', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 3, '2021-02-15', NULL, 'HPV2020C', 'Third dose of 3-dose series'),
    
    ('David Anderson', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-10-08', '2024-10-08', 'FL2023G', 'Annual influenza vaccination'),
    ('David Anderson', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2021-09-22', '2031-09-22', 'TD2021F', 'Tdap booster'),
    
    ('Lisa Garcia', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-10-18', '2024-10-18', 'FL2023H', 'Annual influenza vaccination'),
    ('Lisa Garcia', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2022-03-15', '2032-03-15', 'TD2022G', 'Tdap booster'),
    ('Lisa Garcia', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 1, '2019-05-10', NULL, 'HPV2019A', 'First dose of 3-dose series'),
    ('Lisa Garcia', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'right_arm', 2, '2019-07-10', NULL, 'HPV2019B', 'Second dose of 3-dose series'),
    ('Lisa Garcia', 'HPV vaccine', '90651', 'Gardasil 9', 'Merck',
     'intramuscular', 'left_arm', 3, '2020-01-10', NULL, 'HPV2019C', 'Third dose of 3-dose series'),
    
    ('Kevin Lee', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_arm', 1, '2023-09-30', '2024-09-30', 'FL2023I', 'Annual influenza vaccination'),
    ('Kevin Lee', 'Tetanus, diphtheria, pertussis vaccine', '90715', 'Boostrix', 'GlaxoSmithKline',
     'intramuscular', 'left_arm', 1, '2020-12-05', '2030-12-05', 'TD2020H', 'Tdap booster'),
    
    -- Pediatric Immunizations
    ('Emma Garcia', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_thigh', 1, '2023-10-25', '2024-10-25', 'FL2023J', 'Annual influenza vaccination'),
    ('Emma Garcia', 'DTaP vaccine', '90700', 'Infanrix', 'GlaxoSmithKline',
     'intramuscular', 'left_thigh', 5, '2023-02-28', '2033-02-28', 'DT2023A', 'Fifth dose DTaP series'),
    ('Emma Garcia', 'MMR vaccine', '90707', 'M-M-R II', 'Merck',
     'subcutaneous', 'left_arm', 2, '2023-02-28', NULL, 'MMR2023A', 'Second dose MMR series'),
    ('Emma Garcia', 'Varicella vaccine', '90716', 'Varivax', 'Merck',
     'subcutaneous', 'right_arm', 2, '2023-02-28', NULL, 'VAR2023A', 'Second dose varicella series'),
    
    ('Jacob Martinez', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_thigh', 1, '2023-10-20', '2024-10-20', 'FL2023K', 'Annual influenza vaccination'),
    ('Jacob Martinez', 'DTaP vaccine', '90700', 'Infanrix', 'GlaxoSmithKline',
     'intramuscular', 'left_thigh', 4, '2022-05-14', '2032-05-14', 'DT2022A', 'Fourth dose DTaP series'),
    ('Jacob Martinez', 'MMR vaccine', '90707', 'M-M-R II', 'Merck',
     'subcutaneous', 'left_arm', 1, '2020-05-14', NULL, 'MMR2020A', 'First dose MMR series'),
    ('Jacob Martinez', 'Varicella vaccine', '90716', 'Varivax', 'Merck',
     'subcutaneous', 'right_arm', 1, '2020-05-14', NULL, 'VAR2020A', 'First dose varicella series'),
    
    ('Olivia Smith', 'Influenza vaccine, quadrivalent', '90630', 'Fluzone Quadrivalent', 'Sanofi Pasteur',
     'intramuscular', 'left_thigh', 1, '2023-10-22', '2024-10-22', 'FL2023L', 'Annual influenza vaccination'),
    ('Olivia Smith', 'DTaP vaccine', '90700', 'Infanrix', 'GlaxoSmithKline',
     'intramuscular', 'left_thigh', 5, '2022-09-03', '2032-09-03', 'DT2022B', 'Fifth dose DTaP series'),
    ('Olivia Smith', 'MMR vaccine', '90707', 'M-M-R II', 'Merck',
     'subcutaneous', 'left_arm', 2, '2022-09-03', NULL, 'MMR2022A', 'Second dose MMR series'),
    ('Olivia Smith', 'Varicella vaccine', '90716', 'Varivax', 'Merck',
     'subcutaneous', 'right_arm', 2, '2022-09-03', NULL, 'VAR2022A', 'Second dose varicella series')
  ) AS t(patient_name, vaccine_name, vaccine_code, manufacturer_product, manufacturer, route, site, dose_number, administered_date, expiration_date, lot_number, notes)
)
INSERT INTO public.immunizations (
  id,
  patient_id,
  vaccine_name,
  vaccine_code,
  manufacturer,
  lot_number,
  dose_number,
  route,
  site,
  administered_date,
  administered_by,
  expiration_date,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  pd.patient_id,
  id.vaccine_name,
  id.vaccine_code,
  id.manufacturer,
  id.lot_number,
  id.dose_number,
  id.route::administration_route,
  id.site::administration_site,
  id.administered_date::date,
  pd.administering_provider_id,
  id.expiration_date::date,
  id.notes,
  id.administered_date::date,
  id.administered_date::date + INTERVAL '1 day'
FROM patient_data pd
JOIN immunization_data id ON id.patient_name = pd.patient_name;

COMMIT;
