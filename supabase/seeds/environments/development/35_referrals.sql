-- Start transaction
BEGIN;

-- Insert Referrals
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.referrals (
  id,
  patient_id,
  referring_provider_id,
  referred_to_provider_id,
  referral_date,
  reason,
  status,
  priority,
  notes,
  scheduled_date,
  completed_date,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  referring.id as referring_provider_id,
  referred.id as referred_to_provider_id,
  CASE p.name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '3 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 week'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
  END as referral_date,
  CASE p.name
    WHEN '<PERSON>' THEN 'Ophthalmology evaluation for diabetic retinopathy screening'
    WHEN '<PERSON>' THEN 'Cardiac rehabilitation program'
    WHEN '<PERSON>' THEN 'Pulmonology consultation for asthma management'
    WHEN '<PERSON>' THEN 'ENT evaluation for chronic sinusitis'
    WHEN 'Emma Garcia' THEN 'Developmental assessment'
  END as reason,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'scheduled'::referral_status
    WHEN 'James Wilson' THEN 'completed'::referral_status
    WHEN 'Sophia <PERSON>' THEN 'pending'::referral_status
    WHEN 'David Anderson' THEN 'scheduled'::referral_status
    WHEN 'Emma Garcia' THEN 'pending'::referral_status
  END as status,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'routine'::referral_priority
    WHEN 'James Wilson' THEN 'urgent'::referral_priority
    WHEN 'Sophia Martinez' THEN 'routine'::referral_priority
    WHEN 'David Anderson' THEN 'routine'::referral_priority
    WHEN 'Emma Garcia' THEN 'routine'::referral_priority
  END as priority,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Annual diabetic eye exam'
    WHEN 'James Wilson' THEN 'Post-cardiac event rehabilitation'
    WHEN 'Sophia Martinez' THEN 'Evaluation for possible medication adjustment'
    WHEN 'David Anderson' THEN 'Recurrent sinusitis not responding to antibiotics'
    WHEN 'Emma Garcia' THEN 'Routine developmental assessment'
  END as notes,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() + INTERVAL '2 weeks'
    WHEN 'James Wilson' THEN NULL
    WHEN 'Sophia Martinez' THEN NULL
    WHEN 'David Anderson' THEN NOW() + INTERVAL '1 week'
    WHEN 'Emma Garcia' THEN NULL
  END as scheduled_date,
  CASE p.name
    WHEN 'Alice Thompson' THEN NULL
    WHEN 'James Wilson' THEN NOW() - INTERVAL '2 weeks'
    WHEN 'Sophia Martinez' THEN NULL
    WHEN 'David Anderson' THEN NULL
    WHEN 'Emma Garcia' THEN NULL
  END as completed_date,
  jsonb_build_object(
    'specialty', CASE p.name
      WHEN 'Alice Thompson' THEN 'Ophthalmology'
      WHEN 'James Wilson' THEN 'Cardiology'
      WHEN 'Sophia Martinez' THEN 'Pulmonology'
      WHEN 'David Anderson' THEN 'Otolaryngology'
      WHEN 'Emma Garcia' THEN 'Developmental Pediatrics'
    END
  ) as metadata,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data referring ON
  CASE p.name
    WHEN 'Alice Thompson' THEN referring.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN referring.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN referring.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN referring.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN referring.name = 'Dr. Robert'
  END
JOIN provider_data referred ON
  CASE p.name
    WHEN 'Alice Thompson' THEN referred.name = 'Dr. Michael'
    WHEN 'James Wilson' THEN referred.name = 'Dr. Sarah'
    WHEN 'Sophia Martinez' THEN referred.name = 'Dr. Michael'
    WHEN 'David Anderson' THEN referred.name = 'Dr. Lisa'
    WHEN 'Emma Garcia' THEN referred.name = 'Dr. James'
  END;

COMMIT;
