-- Start transaction
BEGIN;

-- Insert Activity Logs
DO $$
DECLARE
  org_rec RECORD;
  provider_rec RECORD;
  patient_rec RECORD;
  appointment_rec RECORD;
  task_rec RECORD;
  action_type TEXT;
  resource_type TEXT;
  resource_id UUID;
  details_json JSONB;
  activity_date TIMESTAMP;
BEGIN
  -- Create activity logs for each organization
  FOR org_rec IN
    SELECT id, name FROM public.organizations
  LOOP
    -- Create patient-related activities
    FOR patient_rec IN
      SELECT p.id, p.first_name || ' ' || p.last_name AS name, p.organization_id
      FROM public.patients p
      WHERE p.organization_id = org_rec.id
    LOOP
      -- Find a provider in the same organization
      SELECT hp.id, hp.user_id, hp.first_name || ' ' || hp.last_name AS name
      INTO provider_rec
      FROM public.healthcare_providers hp
      WHERE hp.organization_id = org_rec.id
      ORDER BY random()
      LIMIT 1;

      -- Skip if we couldn't find a provider
      CONTINUE WHEN provider_rec IS NULL;

      -- Create 1-3 activities for each patient
      FOR i IN 1..floor(random() * 3) + 1 LOOP
        -- Set action type based on random selection
        CASE floor(random() * 4)::integer
          WHEN 0 THEN action_type := 'create';
          WHEN 1 THEN action_type := 'update';
          WHEN 2 THEN action_type := 'view';
          ELSE action_type := 'delete';
        END CASE;

        -- Set details based on action type
        CASE action_type
          WHEN 'create' THEN details_json := jsonb_build_object(
            'patient_name', patient_rec.name,
            'details', 'Created new patient record'
          );
          WHEN 'update' THEN details_json := jsonb_build_object(
            'patient_name', patient_rec.name,
            'details', 'Updated patient information'
          );
          WHEN 'view' THEN details_json := jsonb_build_object(
            'patient_name', patient_rec.name,
            'details', 'Viewed patient profile'
          );
          ELSE details_json := jsonb_build_object(
            'patient_name', patient_rec.name,
            'details', 'Archived patient record'
          );
        END CASE;

        -- Set activity date
        activity_date := NOW() - (random() * INTERVAL '30 days');

        -- Insert the activity log
        INSERT INTO public.activity_logs (
          id,
          organization_id,
          user_id,
          action_type,
          resource_type,
          resource_id,
          details,
          created_at
        ) VALUES (
          uuid_generate_v4(),
          org_rec.id,
          provider_rec.user_id,
          action_type,
          'patient',
          patient_rec.id,
          details_json,
          activity_date
        );
      END LOOP;
    END LOOP;

    -- Create appointment-related activities
    FOR appointment_rec IN
      SELECT a.id, a.patient_id, a.provider_id, a.appointment_date, a.organization_id
      FROM public.appointments a
      WHERE a.organization_id = org_rec.id
      LIMIT 20
    LOOP
      -- Find the patient and provider
      SELECT p.id, p.first_name || ' ' || p.last_name AS name
      INTO patient_rec
      FROM public.patients p
      WHERE p.id = appointment_rec.patient_id;

      SELECT hp.id, hp.user_id, hp.first_name || ' ' || hp.last_name AS name
      INTO provider_rec
      FROM public.healthcare_providers hp
      WHERE hp.id = appointment_rec.provider_id;

      -- Skip if we couldn't find the patient or provider
      CONTINUE WHEN patient_rec IS NULL OR provider_rec IS NULL;

      -- Set action type based on random selection
      CASE floor(random() * 4)::integer
        WHEN 0 THEN action_type := 'create';
        WHEN 1 THEN action_type := 'update';
        WHEN 2 THEN action_type := 'view';
        ELSE action_type := 'delete';
      END CASE;

      -- Set details based on action type
      CASE action_type
        WHEN 'create' THEN details_json := jsonb_build_object(
          'patient_name', patient_rec.name,
          'provider_name', provider_rec.name,
          'appointment_date', appointment_rec.appointment_date,
          'details', 'Scheduled new appointment'
        );
        WHEN 'update' THEN details_json := jsonb_build_object(
          'patient_name', patient_rec.name,
          'provider_name', provider_rec.name,
          'appointment_date', appointment_rec.appointment_date,
          'details', 'Updated appointment details'
        );
        WHEN 'view' THEN details_json := jsonb_build_object(
          'patient_name', patient_rec.name,
          'provider_name', provider_rec.name,
          'appointment_date', appointment_rec.appointment_date,
          'details', 'Viewed appointment'
        );
        ELSE details_json := jsonb_build_object(
          'patient_name', patient_rec.name,
          'provider_name', provider_rec.name,
          'appointment_date', appointment_rec.appointment_date,
          'details', 'Cancelled appointment'
        );
      END CASE;

      -- Set activity date
      activity_date := NOW() - (random() * INTERVAL '14 days');

      -- Insert the activity log
      INSERT INTO public.activity_logs (
        id,
        organization_id,
        user_id,
        action_type,
        resource_type,
        resource_id,
        details,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        org_rec.id,
        provider_rec.user_id,
        action_type,
        'appointment',
        appointment_rec.id,
        details_json,
        activity_date
      );
    END LOOP;

    -- Create recent activities (within the last 24 hours)
    FOR i IN 1..5 LOOP
      -- Set resource type based on random selection
      CASE floor(random() * 5)::integer
        WHEN 0 THEN resource_type := 'patient';
        WHEN 1 THEN resource_type := 'appointment';
        WHEN 2 THEN resource_type := 'medical_record';
        WHEN 3 THEN resource_type := 'task';
        ELSE resource_type := 'message';
      END CASE;

      -- Find a provider in the organization
      SELECT hp.id, hp.user_id, hp.first_name || ' ' || hp.last_name AS name
      INTO provider_rec
      FROM public.healthcare_providers hp
      WHERE hp.organization_id = org_rec.id
      ORDER BY random()
      LIMIT 1;

      -- Skip if we couldn't find a provider
      CONTINUE WHEN provider_rec IS NULL;

      -- Find a patient in the organization
      SELECT p.id, p.first_name || ' ' || p.last_name AS name
      INTO patient_rec
      FROM public.patients p
      WHERE p.organization_id = org_rec.id
      ORDER BY random()
      LIMIT 1;

      -- Set details
      details_json := jsonb_build_object(
        'details', 'Recent activity in ' || org_rec.name,
        'patient_name', COALESCE(patient_rec.name, 'New Patient')
      );

      -- Set activity date (within the last 24 hours)
      activity_date := NOW() - (random() * INTERVAL '24 hours');

      -- Insert the activity log
      INSERT INTO public.activity_logs (
        id,
        organization_id,
        user_id,
        action_type,
        resource_type,
        resource_id,
        details,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        org_rec.id,
        provider_rec.user_id,
        'update',
        resource_type,
        uuid_generate_v4(),
        details_json,
        activity_date
      );
    END LOOP;
  END LOOP;
END
$$;

COMMIT;
