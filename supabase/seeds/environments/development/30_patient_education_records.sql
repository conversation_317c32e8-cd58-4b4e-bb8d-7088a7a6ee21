-- Start transaction
BEGIN;

-- Insert Patient Education Records
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
),
education_data AS (
  SELECT id, title
  FROM public.education_materials
)
INSERT INTO public.patient_education_records (
  id,
  patient_id,
  material_id,
  provider_id,
  provided_date,
  notes,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  e.id as material_id,
  prov.id as provider_id,
  CASE p.name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '3 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 week'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
  END as provided_date,
  CASE p.name
    WHEN '<PERSON>' THEN 'Patient reports better understanding of hypertension management'
    WHEN '<PERSON>' THEN 'Patient reviewing heart health materials'
    WHEN '<PERSON>' THEN 'Parent reports implementing asthma action plan'
    WHEN '<PERSON>' THEN 'Materials provided at last visit'
    WHEN 'Emma Garcia' THEN 'Parent reviewing vaccination information'
  END as notes,
  CASE p.name
    WHEN 'Alice Thompson' THEN jsonb_build_object('status', 'completed', 'completion_date', (NOW() - INTERVAL '1 week')::text)
    WHEN 'James Wilson' THEN jsonb_build_object('status', 'in_progress')
    WHEN 'Sophia Martinez' THEN jsonb_build_object('status', 'completed', 'completion_date', (NOW() - INTERVAL '2 weeks')::text)
    WHEN 'David Anderson' THEN jsonb_build_object('status', 'not_started')
    WHEN 'Emma Garcia' THEN jsonb_build_object('status', 'in_progress')
  END as metadata,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN prov.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN prov.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN prov.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN prov.name = 'Dr. Robert'
  END
JOIN education_data e ON
  CASE p.name
    WHEN 'Alice Thompson' THEN e.title = 'Understanding Hypertension'
    WHEN 'James Wilson' THEN e.title = 'Heart Health Guidelines'
    WHEN 'Sophia Martinez' THEN e.title = 'Asthma Action Plan'
    WHEN 'David Anderson' THEN e.title = 'Managing Seasonal Allergies'
    WHEN 'Emma Garcia' THEN e.title = 'Childhood Vaccination Schedule'
  END;

COMMIT;
