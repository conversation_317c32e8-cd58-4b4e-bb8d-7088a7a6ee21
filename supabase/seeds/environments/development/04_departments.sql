-- Start transaction
BEGIN;

-- Insert Departments using CTEs
WITH facility_data AS (
  SELECT id, name FROM facilities
),
department_data (id, name, facility_name) AS (
  VALUES
    (gen_random_uuid(), 'Primary Care', 'Spritely Main Hospital'),
    (gen_random_uuid(), 'Cardiology', 'Spritely Main Hospital'),
    (gen_random_uuid(), 'Pediatrics', 'Spritely Main Hospital'),
    (gen_random_uuid(), 'Primary Care', 'Spritely Community Clinic'),
    (gen_random_uuid(), 'Pediatrics', 'Spritely Pediatric Center')
)
INSERT INTO public.departments (
  id,
  facility_id,
  name,
  type,
  settings,
  created_at,
  updated_at
)
SELECT
  d.id,
  f.id as facility_id,
  d.name,
  CASE d.name
    WHEN 'Primary Care' THEN 'primary_care'::department_type
    WHEN 'Cardiology' THEN 'cardiology'::department_type
    WHEN 'Pediatrics' THEN 'pediatrics'::department_type
  END as type,
  CASE d.name
    WHEN 'Primary Care' THEN '{"description": "General primary care services"}'::jsonb
    WHEN 'Cardiology' THEN '{"description": "Specialized heart care"}'::jsonb
    WHEN 'Pediatrics' THEN '{"description": "Child healthcare services"}'::jsonb
  END as settings,
  NOW(),
  NOW()
FROM department_data d
JOIN facility_data f ON f.name = d.facility_name;

-- Commit transaction
COMMIT;