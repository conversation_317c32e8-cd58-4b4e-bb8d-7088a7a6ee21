-- Start transaction
BEGIN;

-- Insert Vital Signs
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
)
INSERT INTO public.vital_signs (id, patient_id, recorded_at, blood_pressure_systolic, blood_pressure_diastolic, heart_rate, respiratory_rate, temperature, temperature_unit, oxygen_saturation, height, weight, bmi, pain_level, notes)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  CASE p.name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '3 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 week'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 days'
    ELSE NOW() - (random() * INTERVAL '30 days')
  END as recorded_at,
  CASE p.name
    WHEN '<PERSON>' THEN 120
    WHEN '<PERSON>' THEN 135
    WHEN '<PERSON>' THEN 100
    WHEN '<PERSON>' THEN 125
    WHEN '<PERSON>' THEN 95
    ELSE floor(random() * 40 + 100)::integer
  END as blood_pressure_systolic,
  CASE p.name
    WHEN '<PERSON>' THEN 80
    WHEN '<PERSON>' THEN 85
    WHEN 'Sophia Martinez' THEN 65
    WHEN '<PERSON>' THEN 75
    WHEN 'Emma Garcia' THEN 60
    ELSE floor(random() * 25 + 60)::integer
  END as blood_pressure_diastolic,
  CASE p.name
    WHEN '<PERSON>' THEN 72
    WHEN 'James Wilson' THEN 78
    WHEN 'Sophia Martinez' THEN 90
    WHEN 'David Anderson' THEN 68
    WHEN 'Emma Garcia' THEN 100
    ELSE floor(random() * 40 + 60)::integer
  END as heart_rate,
  CASE p.name
    WHEN 'Alice Thompson' THEN 16
    WHEN 'James Wilson' THEN 18
    WHEN 'Sophia Martinez' THEN 22
    WHEN 'David Anderson' THEN 14
    WHEN 'Emma Garcia' THEN 24
    ELSE floor(random() * 12 + 12)::integer
  END as respiratory_rate,
  CASE p.name
    WHEN 'Alice Thompson' THEN 37.0
    WHEN 'James Wilson' THEN 37.2
    WHEN 'Sophia Martinez' THEN 37.5
    WHEN 'David Anderson' THEN 38.1
    WHEN 'Emma Garcia' THEN 37.3
    ELSE (36.5 + random() * 2.0)::numeric(3,1)
  END as temperature,
  'C' as temperature_unit,
  CASE p.name
    WHEN 'Alice Thompson' THEN 98
    WHEN 'James Wilson' THEN 97
    WHEN 'Sophia Martinez' THEN 99
    WHEN 'David Anderson' THEN 96
    WHEN 'Emma Garcia' THEN 98
    ELSE floor(random() * 5 + 95)::integer
  END as oxygen_saturation,
  CASE p.name
    WHEN 'Alice Thompson' THEN 165
    WHEN 'James Wilson' THEN 180
    WHEN 'Sophia Martinez' THEN 110
    WHEN 'David Anderson' THEN 175
    WHEN 'Emma Garcia' THEN 90
    ELSE floor(random() * 100 + 100)::integer
  END as height,
  CASE p.name
    WHEN 'Alice Thompson' THEN 65
    WHEN 'James Wilson' THEN 85
    WHEN 'Sophia Martinez' THEN 20
    WHEN 'David Anderson' THEN 70
    WHEN 'Emma Garcia' THEN 14
    ELSE floor(random() * 70 + 20)::integer
  END as weight,
  CASE p.name
    WHEN 'Alice Thompson' THEN 23.9
    WHEN 'James Wilson' THEN 26.2
    WHEN 'Sophia Martinez' THEN 16.5
    WHEN 'David Anderson' THEN 22.9
    WHEN 'Emma Garcia' THEN 17.3
    ELSE (18.0 + random() * 12.0)::numeric(3,1)
  END as bmi,
  CASE p.name
    WHEN 'Alice Thompson' THEN 0
    WHEN 'James Wilson' THEN 2
    WHEN 'Sophia Martinez' THEN 0
    WHEN 'David Anderson' THEN 3
    WHEN 'Emma Garcia' THEN 1
    ELSE floor(random() * 6)::integer
  END as pain_level,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Regular check-up, all vitals normal'
    WHEN 'James Wilson' THEN 'Slightly elevated blood pressure, follow up recommended'
    WHEN 'Sophia Martinez' THEN 'Elevated heart rate, normal for age'
    WHEN 'David Anderson' THEN 'Mild fever, pain reported in throat'
    WHEN 'Emma Garcia' THEN 'Elevated heart rate, normal for age'
    ELSE (ARRAY[
      'Regular check-up, all vitals normal',
      'Routine examination, no concerns',
      'Patient reports feeling well',
      'Vitals within normal range',
      'No significant findings'
    ])[floor(random() * 5 + 1)]
  END as notes
FROM patient_data p;

COMMIT;
