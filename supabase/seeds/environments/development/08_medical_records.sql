-- Start transaction
BEGIN;

-- Insert Medical Records
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
org_data AS (
  SELECT id, name
  FROM public.organizations
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.medical_records (
  id,
  organization_id,
  patient_id,
  provider_id,
  visit_date,
  chief_complaint,
  diagnosis,
  treatment_plan,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  pat.id as patient_id,
  prov.id as provider_id,
  CASE pat.name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '6 months'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '3 months'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 months'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
  END as visit_date,
  CASE pat.name
    WHEN '<PERSON>' THEN 'Follow-up for hypertension'
    WHEN '<PERSON>' THEN 'Chest pain with exertion'
    WHEN '<PERSON>' THEN 'Well-child visit'
    WHEN '<PERSON>' THEN 'Cough, congestion'
    WHEN '<PERSON> Garcia' THEN 'Well-child visit'
  END as chief_complaint,
  CASE pat.name
    WHEN 'Alice Thompson' THEN ARRAY['Hypertension', 'Type 2 Diabetes']
    WHEN 'James Wilson' THEN ARRAY['Coronary Artery Disease', 'Hyperlipidemia']
    WHEN 'Sophia Martinez' THEN ARRAY['Asthma', 'Seasonal Allergies']
    WHEN 'David Anderson' THEN ARRAY['Upper Respiratory Infection']
    WHEN 'Emma Garcia' THEN ARRAY['Well Child Visit']
  END as diagnosis,
  CASE pat.name
    WHEN 'Alice Thompson' THEN 'Continue current medications. Lifestyle modifications. Follow-up in 3 months.'
    WHEN 'James Wilson' THEN 'Adjust medications. Cardiac rehabilitation. Follow-up in 1 month.'
    WHEN 'Sophia Martinez' THEN 'Inhaler technique review. Allergy management plan. Follow-up in 6 months.'
    WHEN 'David Anderson' THEN 'Rest. Hydration. Symptomatic treatment. Follow-up if not improving.'
    WHEN 'Emma Garcia' THEN 'Continue regular check-ups. Update vaccinations. Developmental screening.'
  END as treatment_plan,
  CASE pat.name
    WHEN 'Alice Thompson' THEN 'Patient reports good medication compliance. Blood pressure well-controlled.'
    WHEN 'James Wilson' THEN 'Patient experiencing improved exercise tolerance. Continuing cardiac rehabilitation.'
    WHEN 'Sophia Martinez' THEN 'Asthma well-controlled. No recent exacerbations.'
    WHEN 'David Anderson' THEN 'Symptoms improving with current treatment plan.'
    WHEN 'Emma Garcia' THEN 'Development on track. All milestones appropriate for age.'
  END as notes,
  NOW(),
  NOW()
FROM patient_data pat
JOIN org_data o ON
  CASE pat.name
    WHEN 'Alice Thompson' THEN o.name = 'Spritely Medical Center'
    WHEN 'James Wilson' THEN o.name = 'Spritely Medical Center'
    WHEN 'Sophia Martinez' THEN o.name = 'Spritely Medical Center'
    WHEN 'David Anderson' THEN o.name = 'Spritely Community Clinic'
    WHEN 'Emma Garcia' THEN o.name = 'Spritely Pediatrics'
  END
JOIN provider_data prov ON
  CASE pat.name
    WHEN 'Alice Thompson' THEN prov.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN prov.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN prov.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN prov.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN prov.name = 'Dr. Robert'
  END;

COMMIT;