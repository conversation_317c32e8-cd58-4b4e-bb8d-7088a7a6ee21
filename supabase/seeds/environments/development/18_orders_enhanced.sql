-- Enhanced Medical Orders with Realistic Clinical Ordering Patterns
BEGIN;

-- Create comprehensive medical orders with proper clinical indications
WITH patient_provider_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    p.medical_history,
    EXTRACT(YEAR FROM AGE(p.date_of_birth)) as age,
    hp.id as ordering_provider_id,
    hp.first_name || ' ' || hp.last_name as provider_name
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  WHERE hp.provider_type IN ('doctor', 'specialist')
),
order_templates AS (
  SELECT * FROM (VALUES
    -- Laboratory Orders
    ('<PERSON>', 'lab', 'routine', 'Hemoglobin A1C', 
     '{"test_code": "83036", "test_name": "Hemoglobin A1C", "indication": "Diabetes monitoring", "fasting_required": false}',
     '["E11.9"]', 'Routine diabetes monitoring per ADA guidelines'),
    ('<PERSON>', 'lab', 'routine', 'Comprehensive Metabolic Panel',
     '{"test_code": "80053", "test_name": "Comprehensive Metabolic Panel", "indication": "Diabetes and hypertension monitoring", "fasting_required": true}',
     '["E11.9", "I10"]', 'Monitor kidney function and electrolytes'),
    ('Sarah Johnson', 'lab', 'routine', 'Lipid Panel',
     '{"test_code": "80061", "test_name": "Lipid Panel", "indication": "Cardiovascular risk assessment", "fasting_required": true}',
     '["E78.5"]', 'Annual lipid screening for diabetes patient'),
    
    ('Michael Chen', 'lab', 'stat', 'BNP',
     '{"test_code": "83880", "test_name": "B-type Natriuretic Peptide", "indication": "Heart failure assessment", "fasting_required": false}',
     '["I50.9"]', 'Evaluate heart failure status'),
    ('Michael Chen', 'lab', 'routine', 'PT/INR',
     '{"test_code": "85610", "test_name": "Prothrombin Time with INR", "indication": "Warfarin monitoring", "fasting_required": false}',
     '["I48.91"]', 'Anticoagulation monitoring for atrial fibrillation'),
    
    ('Emily Rodriguez', 'lab', 'routine', 'Complete Blood Count',
     '{"test_code": "85025", "test_name": "Complete Blood Count with Differential", "indication": "Routine screening", "fasting_required": false}',
     '["J45.9"]', 'Baseline CBC for asthma patient'),
    
    ('Robert Williams', 'lab', 'routine', 'Comprehensive Metabolic Panel',
     '{"test_code": "80053", "test_name": "Comprehensive Metabolic Panel", "indication": "Cancer surveillance", "fasting_required": true}',
     '["C78.01"]', 'Monitor organ function during cancer follow-up'),
    
    ('Jessica Taylor', 'lab', 'routine', 'Hemoglobin A1C',
     '{"test_code": "83036", "test_name": "Hemoglobin A1C", "indication": "Type 1 diabetes monitoring", "fasting_required": false}',
     '["E10.9"]', 'Quarterly diabetes monitoring'),
    ('Jessica Taylor', 'lab', 'routine', 'Microalbumin',
     '{"test_code": "82043", "test_name": "Microalbumin, random urine", "indication": "Diabetic nephropathy screening", "fasting_required": false}',
     '["E10.9"]', 'Annual screening for diabetic kidney disease'),
    
    -- Imaging Orders
    ('Michael Chen', 'imaging', 'routine', 'Echocardiogram',
     '{"procedure_code": "93306", "procedure_name": "Echocardiogram, complete", "indication": "Heart failure assessment", "contrast": false}',
     '["I50.9"]', 'Assess left ventricular function'),
    ('Michael Chen', 'imaging', 'routine', 'Chest X-ray',
     '{"procedure_code": "71045", "procedure_name": "Chest X-ray, single view", "indication": "Heart failure monitoring", "contrast": false}',
     '["I50.9"]', 'Evaluate for pulmonary edema'),
    
    ('Emily Rodriguez', 'imaging', 'routine', 'Chest X-ray',
     '{"procedure_code": "71045", "procedure_name": "Chest X-ray, single view", "indication": "Asthma evaluation", "contrast": false}',
     '["J45.9"]', 'Rule out pneumonia in asthma exacerbation'),
    
    ('Robert Williams', 'imaging', 'routine', 'CT Chest',
     '{"procedure_code": "71250", "procedure_name": "CT chest without contrast", "indication": "Cancer surveillance", "contrast": false}',
     '["C78.01"]', 'Routine surveillance for lung cancer recurrence'),
    
    ('Kevin Lee', 'imaging', 'routine', 'MRI Lumbar Spine',
     '{"procedure_code": "72148", "procedure_name": "MRI lumbar spine without contrast", "indication": "Back pain evaluation", "contrast": false}',
     '["M54.5"]', 'Evaluate chronic low back pain'),
    
    -- Medication Orders
    ('Sarah Johnson', 'medication', 'routine', 'Metformin',
     '{"medication": "metformin", "dose": "1000", "unit": "mg", "route": "oral", "frequency": "twice daily", "duration": "90 days"}',
     '["E11.9"]', 'First-line therapy for Type 2 diabetes'),
    ('Sarah Johnson', 'medication', 'routine', 'Lisinopril',
     '{"medication": "lisinopril", "dose": "10", "unit": "mg", "route": "oral", "frequency": "once daily", "duration": "90 days"}',
     '["I10"]', 'ACE inhibitor for hypertension and renal protection'),
    
    ('Michael Chen', 'medication', 'routine', 'Carvedilol',
     '{"medication": "carvedilol", "dose": "3.125", "unit": "mg", "route": "oral", "frequency": "twice daily", "duration": "30 days"}',
     '["I50.9"]', 'Beta-blocker for heart failure'),
    ('Michael Chen', 'medication', 'routine', 'Furosemide',
     '{"medication": "furosemide", "dose": "40", "unit": "mg", "route": "oral", "frequency": "once daily", "duration": "30 days"}',
     '["I50.9"]', 'Loop diuretic for fluid management'),
    
    ('Emily Rodriguez', 'medication', 'routine', 'Prednisone',
     '{"medication": "prednisone", "dose": "40", "unit": "mg", "route": "oral", "frequency": "once daily", "duration": "5 days"}',
     '["J45.9"]', 'Oral corticosteroid for asthma exacerbation'),
    
    ('Robert Williams', 'medication', 'routine', 'Tiotropium',
     '{"medication": "tiotropium", "dose": "18", "unit": "mcg", "route": "inhalation", "frequency": "once daily", "duration": "30 days"}',
     '["J44.1"]', 'Long-acting bronchodilator for COPD'),
    
    -- Procedure Orders
    ('Sarah Johnson', 'procedure', 'routine', 'Diabetic Eye Exam',
     '{"procedure": "ophthalmology_exam", "indication": "diabetic_retinopathy_screening", "urgency": "routine"}',
     '["E11.9"]', 'Annual diabetic retinopathy screening'),
    
    ('Michael Chen', 'procedure', 'routine', 'Cardiac Rehabilitation',
     '{"procedure": "cardiac_rehab", "indication": "heart_failure", "sessions": "36", "frequency": "3x_weekly"}',
     '["I50.9"]', 'Phase II cardiac rehabilitation program'),
    
    ('Emily Rodriguez', 'procedure', 'routine', 'Pulmonary Function Test',
     '{"procedure": "spirometry", "indication": "asthma_monitoring", "pre_post_bronchodilator": true}',
     '["J45.9"]', 'Baseline pulmonary function assessment'),
    
    ('Kevin Lee', 'procedure', 'routine', 'Physical Therapy Evaluation',
     '{"procedure": "pt_evaluation", "indication": "back_pain", "frequency": "2x_weekly", "duration": "6_weeks"}',
     '["M54.5"]', 'Physical therapy for chronic low back pain'),
    
    -- Referral Orders
    ('Sarah Johnson', 'referral', 'routine', 'Endocrinology Consultation',
     '{"specialty": "endocrinology", "indication": "diabetes_management", "urgency": "routine"}',
     '["E11.9"]', 'Specialist consultation for diabetes optimization'),
    
    ('Emily Rodriguez', 'referral', 'routine', 'Allergy/Immunology',
     '{"specialty": "allergy_immunology", "indication": "asthma_triggers", "urgency": "routine"}',
     '["J45.9"]', 'Allergy testing for asthma trigger identification'),
    
    ('Robert Williams', 'referral', 'routine', 'Pulmonary Rehabilitation',
     '{"specialty": "pulmonary_rehab", "indication": "post_surgical_conditioning", "urgency": "routine"}',
     '["C78.01", "J44.1"]', 'Pulmonary rehabilitation post-lobectomy'),
    
    ('Kevin Lee', 'referral', 'routine', 'Pain Management',
     '{"specialty": "pain_management", "indication": "chronic_pain", "urgency": "routine"}',
     '["M54.5"]', 'Pain management consultation for chronic back pain')
  ) AS t(patient_name, order_type, priority, order_name, order_details, diagnosis_codes, notes)
)
INSERT INTO public.orders (
  id,
  patient_id,
  ordering_provider_id,
  order_type,
  priority,
  order_details,
  diagnosis_codes,
  notes,
  ordered_at,
  scheduled_date,
  status,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  ppd.patient_id,
  ppd.ordering_provider_id,
  ot.order_type::order_type,
  ot.priority::order_priority,
  ot.order_details::jsonb,
  ot.diagnosis_codes::jsonb,
  ot.notes,
  NOW() - (random() * INTERVAL '30 days'),
  CASE 
    WHEN ot.order_type = 'lab' AND ot.priority = 'stat' THEN NOW() + INTERVAL '2 hours'
    WHEN ot.order_type = 'lab' THEN NOW() + (random() * INTERVAL '7 days')
    WHEN ot.order_type = 'imaging' THEN NOW() + (random() * INTERVAL '14 days')
    WHEN ot.order_type = 'procedure' THEN NOW() + (random() * INTERVAL '30 days')
    ELSE NOW() + (random() * INTERVAL '21 days')
  END,
  CASE 
    WHEN random() < 0.7 THEN 'pending'::order_status
    WHEN random() < 0.85 THEN 'approved'::order_status
    WHEN random() < 0.95 THEN 'in_progress'::order_status
    ELSE 'completed'::order_status
  END,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '7 days')
FROM patient_provider_data ppd
JOIN order_templates ot ON ot.patient_name = ppd.patient_name;

COMMIT;
