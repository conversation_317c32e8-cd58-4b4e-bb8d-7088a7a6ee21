-- Enhanced Medical Records with Realistic Clinical Data and ICD-10 Codes
BEGIN;

-- Create comprehensive medical records with proper diagnoses and clinical documentation
WITH patient_provider_data AS (
  SELECT 
    p.id as patient_id,
    p.first_name || ' ' || p.last_name as patient_name,
    p.organization_id,
    hp.id as provider_id,
    hp.first_name || ' ' || hp.last_name as provider_name,
    d.id as department_id
  FROM public.patients p
  JOIN public.healthcare_providers hp ON hp.organization_id = p.organization_id
  JOIN public.departments d ON d.facility_id IN (
    SELECT f.id FROM public.facilities f WHERE f.organization_id = p.organization_id
  )
  WHERE hp.provider_type IN ('doctor', 'specialist')
),
medical_record_data AS (
  SELECT * FROM (VALUES
    -- Complex Medical Center Cases
    ('<PERSON> Johnson', 'Type 2 Diabetes with complications', 
     '["E11.40", "I10", "E78.5"]', 
     'Patient presents for routine diabetes management. HbA1c 8.2%, indicating suboptimal control. Blood pressure elevated at 150/95. Lipid panel shows total cholesterol 245 mg/dL. Discussed medication adherence and lifestyle modifications. Increased metformin to 1000mg BID. Added lisinopril 10mg daily for hypertension. Referred to nutritionist for diabetes education.',
     'Continue current diabetes medications with dose adjustment. Blood pressure management initiated. Follow-up in 3 months with repeat HbA1c and lipid panel. Ophthalmology referral for diabetic retinopathy screening. Podiatry referral for diabetic foot care.'),
    
    ('Michael Chen', 'Coronary artery disease with heart failure',
     '["I25.10", "I50.9", "I48.91"]',
     'Patient with known CAD s/p CABG presents with worsening dyspnea on exertion. Echo shows EF 35%, down from 45% six months ago. Atrial fibrillation rate controlled. BNP elevated at 850 pg/mL. Physical exam reveals bilateral lower extremity edema and crackles at lung bases.',
     'Heart failure management optimized. Increased furosemide to 40mg daily. Started carvedilol 3.125mg BID. Continue warfarin for anticoagulation. Cardiac rehabilitation referral. Strict fluid restriction 2L daily. Follow-up in 2 weeks.'),
    
    ('Emily Rodriguez', 'Asthma exacerbation with anxiety',
     '["J45.9", "F41.9", "K21.9"]',
     'Young female presents with acute asthma exacerbation triggered by seasonal allergies. Peak flow 60% of personal best. Anxiety symptoms worsening with breathing difficulties. GERD symptoms contributing to nocturnal asthma. No signs of pneumonia on chest X-ray.',
     'Prednisone 40mg daily x 5 days. Increased albuterol inhaler use. Started fluticasone/salmeterol 250/50 BID. Anxiety management with counseling referral. GERD treatment with omeprazole 20mg daily. Allergy testing recommended.'),
    
    ('Robert Williams', 'Lung cancer with COPD',
     '["C78.01", "J44.1", "F32.9"]',
     'Patient with stage IIIA NSCLC s/p right upper lobectomy and adjuvant chemotherapy. COPD exacerbation with increased dyspnea and productive cough. Depression screening positive with PHQ-9 score of 16. Oncology follow-up shows no evidence of recurrence on recent CT scan.',
     'COPD management with bronchodilators and steroids. Pulmonary rehabilitation continuation. Depression treatment with sertraline 50mg daily. Oncology follow-up in 3 months. Smoking cessation counseling reinforcement.'),
    
    -- Community Clinic Cases  
    ('David Anderson', 'Hypertension and obesity',
     '["I10", "E66.9", "G47.33"]',
     'Middle-aged male with poorly controlled hypertension despite medication. BMI 34.2 kg/m². Sleep apnea diagnosed on recent sleep study. Patient reports medication non-adherence due to side effects. Blood pressure 165/98 mmHg.',
     'Medication adjustment for better tolerance. CPAP therapy initiated for sleep apnea. Weight loss counseling and referral to nutritionist. Home blood pressure monitoring. Follow-up in 4 weeks.'),
    
    ('Lisa Garcia', 'Postpartum care with anemia',
     '["Z39.2", "D50.9", "O90.81"]',
     'Postpartum visit 6 weeks after delivery. Patient reports fatigue and heavy menstrual bleeding since delivery. Hemoglobin 9.2 g/dL, indicating iron deficiency anemia. Breastfeeding well established. No signs of postpartum depression.',
     'Iron supplementation with ferrous sulfate 325mg TID. Dietary counseling for iron-rich foods. Contraception counseling provided. Follow-up CBC in 6 weeks. Annual gynecologic exam scheduled.'),
    
    ('Kevin Lee', 'Chronic low back pain',
     '["M54.5", "G43.909"]',
     'Patient with chronic low back pain s/p L4-L5 discectomy. Pain level 6/10, limiting daily activities. MRI shows mild recurrent disc herniation. Migraine headaches increasing in frequency, possibly medication overuse headaches.',
     'Physical therapy referral for core strengthening. Gabapentin 300mg TID for neuropathic pain. Migraine prophylaxis with topiramate 25mg daily. Avoid opioid medications. Pain management referral if no improvement.'),
    
    -- Pediatric Cases
    ('Emma Garcia', 'Pediatric asthma with eczema',
     '["J45.9", "L20.9", "T78.1"]',
     '5-year-old female with well-controlled asthma presents for routine follow-up. Recent eczema flare on arms and legs. Food allergy to eggs and tree nuts confirmed by testing. Growth and development appropriate for age. Immunizations up to date.',
     'Continue current asthma medications. Topical corticosteroids for eczema flare. Strict avoidance of known food allergens. EpiPen prescription provided. Allergy action plan reviewed with parents.'),
    
    ('Jacob Martinez', 'ADHD with speech delay',
     '["F90.9", "F80.9"]',
     '4-year-old male with ADHD and speech delay. Behavioral interventions showing some improvement. Speech therapy twice weekly. Parents report difficulty with attention and hyperactivity at home and preschool. Developmental assessment shows delays in expressive language.',
     'Continue behavioral interventions and speech therapy. Consider medication evaluation if behavioral strategies insufficient. Preschool accommodations discussed. Follow-up in 3 months to assess progress.'),
    
    ('Olivia Smith', 'Type 1 diabetes management',
     '["E10.9", "Z79.4"]',
     '6-year-old female with Type 1 diabetes on insulin pump therapy. HbA1c 7.8%, which is acceptable for age. Recent episode of mild DKA due to pump malfunction. Parents well-educated on diabetes management. Growth and development normal.',
     'Insulin pump settings adjusted. Continuous glucose monitor recommended. Diabetes education reinforcement. Endocrinology follow-up in 3 months. School diabetes management plan updated.')
  ) AS t(patient_name, chief_complaint, diagnosis, notes, treatment_plan)
)
INSERT INTO public.medical_records (
  id,
  organization_id,
  patient_id,
  provider_id,
  department_id,
  visit_date,
  chief_complaint,
  diagnosis,
  notes,
  treatment_plan,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  ppd.organization_id,
  ppd.patient_id,
  ppd.provider_id,
  ppd.department_id,
  NOW() - (random() * INTERVAL '90 days'),
  mrd.chief_complaint,
  mrd.diagnosis::text[],
  mrd.notes,
  mrd.treatment_plan,
  NOW() - (random() * INTERVAL '90 days'),
  NOW() - (random() * INTERVAL '30 days')
FROM patient_provider_data ppd
JOIN medical_record_data mrd ON mrd.patient_name = ppd.patient_name
WHERE ppd.provider_id IS NOT NULL
ORDER BY random()
LIMIT 1;

-- Create additional medical records for follow-up visits
INSERT INTO public.medical_records (
  id,
  organization_id,
  patient_id,
  provider_id,
  department_id,
  visit_date,
  chief_complaint,
  diagnosis,
  notes,
  treatment_plan,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  ppd.organization_id,
  ppd.patient_id,
  ppd.provider_id,
  ppd.department_id,
  NOW() - (random() * INTERVAL '30 days'),
  'Follow-up visit',
  ARRAY['Z09'],
  'Follow-up visit for previously diagnosed condition. Patient reports ' || 
  CASE (random() * 3)::int
    WHEN 0 THEN 'improvement in symptoms with current treatment plan.'
    WHEN 1 THEN 'stable condition with good medication adherence.'
    ELSE 'some concerns that need to be addressed.'
  END,
  CASE (random() * 3)::int
    WHEN 0 THEN 'Continue current medications. Follow-up as scheduled.'
    WHEN 1 THEN 'Medication adjustment made. Monitor for side effects.'
    ELSE 'Additional testing ordered. Follow-up in 2 weeks.'
  END,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '7 days')
FROM patient_provider_data ppd
WHERE random() < 0.7;

COMMIT;
