-- Start transaction
BEGIN;

-- Insert Documents
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
org_data AS (
  SELECT id, name
  FROM public.organizations
),
provider_data AS (
  SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name, hp.user_id
  FROM public.healthcare_providers hp
)
INSERT INTO public.documents (
  id,
  organization_id,
  patient_id,
  document_type,
  title,
  description,
  file_path,
  mime_type,
  metadata,
  created_by,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  pat.id as patient_id,
  CASE pat.name
    WHEN '<PERSON>' THEN 'lab_report'
    WHEN '<PERSON>' THEN 'imaging'
    WHEN '<PERSON>' THEN 'vaccination_record'
    WHEN '<PERSON>' THEN 'prescription'
    WHEN '<PERSON>' THEN 'growth_chart'
  END as document_type,
  CASE pat.name
    WHEN '<PERSON>' THEN 'Complete Blood Count Results'
    WHEN '<PERSON>' THEN 'Cardiac CT Scan Report'
    WHEN '<PERSON>' THEN 'Vaccination Record 2024'
    WHEN '<PERSON>' THEN 'Prescription - Antibiotics'
    WHEN '<PERSON>' THEN 'Growth Chart 2024'
  END as title,
  CASE pat.name
    WHEN '<PERSON>' THEN 'CBC results within normal range. Glucose slightly elevated at 126 mg/dL.'
    WHEN 'James Wilson' THEN 'Cardiac CT shows mild coronary artery calcification. No significant stenosis.'
    WHEN 'Sophia Martinez' THEN 'All vaccinations up to date according to CDC schedule.'
    WHEN 'David Anderson' THEN 'Amoxicillin 500mg - Take 1 capsule every 8 hours for 10 days'
    WHEN '<PERSON>' THEN 'Height and weight tracking appropriately on growth curve.'
  END as description,
  CASE pat.name
    WHEN 'Alice Thompson' THEN '/documents/lab_reports/cbc_alice_thompson.pdf'
    WHEN 'James Wilson' THEN '/documents/imaging/cardiac_ct_james_wilson.pdf'
    WHEN 'Sophia Martinez' THEN '/documents/vaccination/record_sophia_martinez.pdf'
    WHEN 'David Anderson' THEN '/documents/prescriptions/amoxicillin_david_anderson.pdf'
    WHEN 'Emma Garcia' THEN '/documents/growth_charts/emma_garcia_2024.pdf'
  END as file_path,
  CASE pat.name
    WHEN 'Alice Thompson' THEN 'application/pdf'
    WHEN 'James Wilson' THEN 'application/pdf'
    WHEN 'Sophia Martinez' THEN 'application/pdf'
    WHEN 'David Anderson' THEN 'application/pdf'
    WHEN 'Emma Garcia' THEN 'application/pdf'
  END as mime_type,
  CASE pat.name
    WHEN 'Alice Thompson' THEN '{"lab_id": "LAB123", "collection_date": "2024-01-15", "result_date": "2024-01-16"}'::jsonb
    WHEN 'James Wilson' THEN '{"study_date": "2024-02-01", "radiologist": "Dr. Jane Smith", "study_id": "CT456"}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"last_updated": "2024-03-01", "next_due": "2024-09-01"}'::jsonb
    WHEN 'David Anderson' THEN '{"prescription_id": "RX789", "expiration": "2024-04-15"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"chart_date": "2024-03-15", "percentile_height": 75, "percentile_weight": 60}'::jsonb
  END as metadata,
  prov.user_id as created_by,
  NOW(),
  NOW()
FROM patient_data pat
JOIN org_data o ON
  CASE pat.name
    WHEN 'Alice Thompson' THEN o.name = 'Spritely Medical Center'
    WHEN 'James Wilson' THEN o.name = 'Spritely Medical Center'
    WHEN 'Sophia Martinez' THEN o.name = 'Spritely Medical Center'
    WHEN 'David Anderson' THEN o.name = 'Spritely Community Clinic'
    WHEN 'Emma Garcia' THEN o.name = 'Spritely Pediatrics'
  END
JOIN provider_data prov ON
  CASE pat.name
    WHEN 'Alice Thompson' THEN prov.name = 'Dr. Sarah'
    WHEN 'James Wilson' THEN prov.name = 'Dr. Michael'
    WHEN 'Sophia Martinez' THEN prov.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN prov.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN prov.name = 'Dr. Robert'
  END;

COMMIT;