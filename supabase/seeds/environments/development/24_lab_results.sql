-- Start transaction
BEGIN;

-- Insert Lab Results
WITH order_data AS (
  SELECT o.id, o.patient_id, o.ordering_provider_id, p.first_name || ' ' || p.last_name AS patient_name
  FROM public.orders o
  JOIN public.patients p ON o.patient_id = p.id
  WHERE o.order_type = 'lab'
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.lab_results (
  id,
  patient_id,
  provider_id,
  test_name,
  test_date,
  results,
  normal_range,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.patient_id,
  o.ordering_provider_id as provider_id,
  CASE o.patient_name
    WHEN '<PERSON>' THEN 'Complete Blood Count'
    WHEN '<PERSON>' THEN 'Strep Test'
    WHEN '<PERSON>' THEN 'Allergy Panel'
    ELSE 'Basic Metabolic Panel'
  END as test_name,
  CASE o.patient_name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 day'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 day'
    ELSE NOW() - INTERVAL '3 days'
  END as test_date,
  CASE o.patient_name
    WHEN '<PERSON>' THEN '{"WBC": 7.2, "RBC": 4.8, "Hemoglobin": 14.1, "Hematocrit": 42, "Platelets": 250}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"Result": "Positive"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"Egg": "Positive", "Milk": "Negative", "Peanut": "Negative", "Wheat": "Negative"}'::jsonb
    ELSE '{"Glucose": 95, "BUN": 15, "Creatinine": 0.9, "Sodium": 140, "Potassium": 4.2}'::jsonb
  END as results,
  CASE o.patient_name
    WHEN 'Alice Thompson' THEN '{"WBC": "4.5-11.0", "RBC": "4.2-5.4", "Hemoglobin": "12.0-16.0", "Hematocrit": "37-47", "Platelets": "150-400"}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"Result": "Negative"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"Egg": "Negative", "Milk": "Negative", "Peanut": "Negative", "Wheat": "Negative"}'::jsonb
    ELSE '{"Glucose": "70-99", "BUN": "7-20", "Creatinine": "0.6-1.2", "Sodium": "135-145", "Potassium": "3.5-5.0"}'::jsonb
  END as normal_range,
  CASE o.patient_name
    WHEN 'Alice Thompson' THEN 'CBC with differential, all values within normal range except slightly elevated platelets'
    WHEN 'Sophia Martinez' THEN 'Positive for Group A Streptococcus'
    WHEN 'Emma Garcia' THEN 'Positive for egg allergy, other allergens negative'
    ELSE 'Basic metabolic panel within normal limits'
  END as notes,
  NOW(),
  NOW()
FROM order_data o
WHERE o.patient_name IN ('Alice Thompson', 'Sophia Martinez', 'Emma Garcia');

COMMIT;
