-- Start transaction
BEGIN;

-- Insert Task Comments
WITH task_data AS (
  SELECT t.id, t.title, p.id as patient_id, p.first_name || ' ' || p.last_name AS patient_name
  FROM public.tasks t
  JOIN public.patients p ON (t.related_to->>'patient_id')::uuid = p.id
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.task_comments (
  id,
  task_id,
  user_id,
  content,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  t.id as task_id,
  p.user_id as user_id,
  CASE t.patient_name
    WHEN '<PERSON>' THEN 'Left voicemail for patient regarding lab results'
    WHEN '<PERSON>' THEN 'Echocardiogram results received, need to review before next appointment'
    WHEN '<PERSON>' THEN 'Updated vaccination record with recent immunizations'
    WHEN '<PERSON>' THEN 'Pharmacy confirmed prescription was received'
    WHEN '<PERSON>' THEN 'Scheduled follow-up appointment for next month'
  E<PERSON> as content,
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days'
FROM task_data t
JOIN provider_data p ON
  CASE t.patient_name
    WHEN '<PERSON>' THEN p.name = 'Dr. <PERSON>'
    WHEN '<PERSON>' THEN p.name = 'Dr. <PERSON>'
    WHEN 'Sophia Martinez' THEN p.name = 'Dr. Lisa'
    WHEN 'David Anderson' THEN p.name = 'Dr. James'
    WHEN 'Emma Garcia' THEN p.name = 'Dr. Robert'
  END;

-- Insert Task Watchers
WITH task_data AS (
  SELECT t.id, t.title, p.id as patient_id, p.first_name || ' ' || p.last_name AS patient_name
  FROM public.tasks t
  JOIN public.patients p ON (t.related_to->>'patient_id')::uuid = p.id
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.task_watchers (
  id,
  task_id,
  user_id,
  created_at
)
SELECT
  uuid_generate_v4(),
  t.id as task_id,
  p.user_id as user_id,
  NOW() - INTERVAL '1 week'
FROM task_data t
JOIN provider_data p ON
  CASE t.patient_name
    WHEN 'Alice Thompson' THEN p.name IN ('Dr. Sarah', 'Dr. Michael')
    WHEN 'James Wilson' THEN p.name IN ('Dr. Michael', 'Dr. Sarah')
    WHEN 'Sophia Martinez' THEN p.name IN ('Dr. Lisa', 'Dr. James')
    WHEN 'David Anderson' THEN p.name IN ('Dr. James', 'Dr. Sarah')
    WHEN 'Emma Garcia' THEN p.name IN ('Dr. Robert', 'Dr. Lisa')
  END;

COMMIT;
