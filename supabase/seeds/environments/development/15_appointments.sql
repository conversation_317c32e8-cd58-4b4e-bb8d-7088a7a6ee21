-- Start transaction
BEGIN;

-- Insert past appointments (completed or cancelled)
INSERT INTO public.appointments (
  id,
  organization_id,
  department_id,
  patient_id,
  provider_id,
  appointment_date,
  duration_minutes,
  status,
  reason,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.organization_id,
  hp.department_id,
  p.id,
  hp.id,
  NOW() - (random() * INTERVAL '90 days'),
  (ARRAY[15, 30, 45, 60])[floor(random() * 4 + 1)],
  (ARRAY['completed', 'cancelled', 'no_show'])[floor(random() * 3 + 1)]::appointment_status,
  (ARRAY['Annual physical', 'Follow-up', 'Consultation', 'Sick visit', 'Vaccination', 'Lab work'])[floor(random() * 6 + 1)],
  CASE
    WHEN random() < 0.7 THEN 'Patient seen for routine visit. No significant concerns.'
    WHEN random() < 0.8 THEN 'Patient reported symptoms of ' ||
      (ARRAY['cold', 'flu', 'allergies', 'headache', 'back pain', 'stomach pain'])[floor(random() * 6 + 1)] || '.'
    WHEN random() < 0.9 THEN 'Follow-up for ' ||
      (ARRAY['medication adjustment', 'lab results', 'imaging results', 'specialist referral'])[floor(random() * 4 + 1)] || '.'
    ELSE NULL
  END,
  NOW() - (random() * INTERVAL '120 days'),
  NOW() - (random() * INTERVAL '90 days')
FROM
  public.patients p,
  public.healthcare_providers hp
WHERE
  p.organization_id = hp.organization_id
  AND hp.provider_type = 'doctor'
  AND random() < 0.3
LIMIT 50;

-- Insert today's appointments (checked_in, in_progress, or scheduled)
INSERT INTO public.appointments (
  id,
  organization_id,
  department_id,
  patient_id,
  provider_id,
  appointment_date,
  duration_minutes,
  status,
  reason,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.organization_id,
  hp.department_id,
  p.id,
  hp.id,
  NOW() + (random() * INTERVAL '8 hours'),
  (ARRAY[15, 30, 45, 60])[floor(random() * 4 + 1)],
  (ARRAY['scheduled', 'checked_in', 'in_progress'])[floor(random() * 3 + 1)]::appointment_status,
  (ARRAY['Annual physical', 'Follow-up', 'Consultation', 'Sick visit', 'Vaccination', 'Lab work'])[floor(random() * 6 + 1)],
  NULL,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '1 day')
FROM
  public.patients p,
  public.healthcare_providers hp
WHERE
  p.organization_id = hp.organization_id
  AND hp.provider_type = 'doctor'
  AND random() < 0.2
LIMIT 20;

-- Insert future appointments (scheduled)
INSERT INTO public.appointments (
  id,
  organization_id,
  department_id,
  patient_id,
  provider_id,
  appointment_date,
  duration_minutes,
  status,
  reason,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.organization_id,
  hp.department_id,
  p.id,
  hp.id,
  NOW() + (random() * INTERVAL '30 days'),
  (ARRAY[15, 30, 45, 60])[floor(random() * 4 + 1)],
  'scheduled'::appointment_status,
  (ARRAY['Annual physical', 'Follow-up', 'Consultation', 'Sick visit', 'Vaccination', 'Lab work'])[floor(random() * 6 + 1)],
  NULL,
  NOW() - (random() * INTERVAL '30 days'),
  NOW() - (random() * INTERVAL '1 day')
FROM
  public.patients p,
  public.healthcare_providers hp
WHERE
  p.organization_id = hp.organization_id
  AND hp.provider_type = 'doctor'
  AND random() < 0.2
LIMIT 30;

COMMIT;
