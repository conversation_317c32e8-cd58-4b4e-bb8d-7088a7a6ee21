{"root": ["./src/types.d.ts", "./src/vite-env.d.ts", "./src/components/index.ts", "./src/components/settings/index.ts", "./src/components/ui/navigation-menu-styles.ts", "./src/contexts/commandpalettecontext.ts", "./src/contexts/auth-context-types.ts", "./src/features/command-palette/index.ts", "./src/features/command-palette/types/shortcuts.ts", "./src/features/command-palette/utils/shortcutmanager.ts", "./src/hooks/use-mobile.ts", "./src/hooks/useauth.ts", "./src/hooks/usecommandpalette.ts", "./src/hooks/usenotifications.ts", "./src/hooks/userenderoptimizer.ts", "./src/hooks/useuserroles.ts", "./src/hooks/useusersettings.ts", "./src/hooks/dashboard/useactivity.ts", "./src/hooks/dashboard/useanalytics.ts", "./src/hooks/dashboard/useappointments.ts", "./src/hooks/dashboard/usepatients.ts", "./src/hooks/dashboard/usetasks.ts", "./src/layouts/index.ts", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/lib/auth/auth-service.ts", "./src/lib/auth/auth-state-machine.ts", "./src/lib/auth/organization-cache-fix.ts", "./src/lib/auth/organization-cache.ts", "./src/lib/auth/organization-service.ts", "./src/lib/auth/organization-types.ts", "./src/lib/search/types.ts", "./src/pages/index.ts", "./src/pages/home/<USER>", "./src/pages/home/<USER>/index.ts", "./src/pages/home/<USER>/navbar/data.ts", "./src/pages/home/<USER>/navbar/index.ts", "./src/types/database.ts", "./src/types/settings.ts", "./src/app.tsx", "./src/main.tsx", "./src/components/commandpalette.tsx", "./src/components/errorboundary.tsx", "./src/components/patientlist.tsx", "./src/components/renderoptimizedexample.tsx", "./src/components/theme-provider.tsx", "./src/components/appointments/appointments.tsx", "./src/components/auth/forgotpasswordform.tsx", "./src/components/auth/loginform.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/auth/registerform.tsx", "./src/components/auth/resetpasswordform.tsx", "./src/components/dashboard/dashboard.tsx", "./src/components/dashboard/dashboardsidebar.tsx", "./src/components/dashboard/organizationheader.tsx", "./src/components/dashboard/organizationswitcher.tsx", "./src/components/dashboard/userprofileheader.tsx", "./src/components/dashboard/activity/recentactivity.tsx", "./src/components/dashboard/analytics/appointmentmetrics.tsx", "./src/components/dashboard/analytics/patientdemographics.tsx", "./src/components/dashboard/appointments/upcomingappointments.tsx", "./src/components/dashboard/patients/recentpatients.tsx", "./src/components/dashboard/stats/statsoverview.tsx", "./src/components/dashboard/tasks/taskslist.tsx", "./src/components/loading/loadingscreen.tsx", "./src/components/loading/loadingstatemanager.tsx", "./src/components/navigation/navigationmanager.tsx", "./src/components/organization/organizationselector.tsx", "./src/components/organization/organizationswitcher.tsx", "./src/components/organization/rolebasedorgselector.tsx", "./src/components/patients/patients.tsx", "./src/components/providers/commandpaletteprovider.tsx", "./src/components/settings/appearancesettings.tsx", "./src/components/settings/keyboardshortcuts.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/profilesettings.tsx", "./src/components/settings/securitysettings.tsx", "./src/components/settings/settings.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/auth-debugger.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/decorative-background.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/loading-screen.tsx", "./src/components/ui/loading-spinner.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/theme-toggle.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.tsx", "./src/contexts/authcontext.tsx", "./src/contexts/authprovider.tsx", "./src/contexts/dropdownmenucontext.tsx", "./src/contexts/auth-context.tsx", "./src/features/command-palette/components/commandpalette.tsx", "./src/features/command-palette/providers/commandpaletteprovider.tsx", "./src/layouts/authlayout.tsx", "./src/layouts/dashboardlayout.tsx", "./src/layouts/mainlayout.tsx", "./src/pages/dashboard/dashboardpage.tsx", "./src/pages/error/notfoundpage.tsx", "./src/pages/home/<USER>", "./src/pages/home/<USER>", "./src/pages/home/<USER>/cta.tsx", "./src/pages/home/<USER>/features.tsx", "./src/pages/home/<USER>/footer.tsx", "./src/pages/home/<USER>/hero.tsx", "./src/pages/home/<USER>/stats.tsx", "./src/pages/home/<USER>/testimonials.tsx", "./src/pages/home/<USER>/navbar/listitem.tsx", "./src/pages/home/<USER>/navbar/menucomponents.tsx", "./src/pages/home/<USER>/navbar/navbar.tsx", "./src/pages/home/<USER>/navbar/pricingitem.tsx", "./src/pages/organizations/organizationsettingspage.tsx", "./src/pages/organizations/organizationspage.tsx", "./src/pages/setup/basicsetuppage.tsx", "./src/pages/setup/createorganizationpage.tsx", "./src/pages/setup/fullsetuppage.tsx", "./src/pages/setup/invitedusersetup.tsx", "./src/pages/setup/setuppage.tsx", "./src/pages/setup/setuprouter.tsx", "./src/pages/setup/simplesetuppage.tsx", "./src/pages/setup/standalonesetuppage.tsx", "./src/providers/theme-provider.tsx"], "version": "5.8.3"}