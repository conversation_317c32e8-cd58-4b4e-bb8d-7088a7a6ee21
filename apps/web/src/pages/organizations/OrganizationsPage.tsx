
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Organization as ContextOrganization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { Building2, Plus, Settings } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Define simplified types for what we actually need
interface Organization {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
}

interface UserRoleWithOrganization {
  role: string;
  invitation_status?: string | null;
  organization: Organization;
}

export function OrganizationsPage() {
  const { user, organization: currentOrg, setOrganization } = useAuth();
  const navigate = useNavigate();
  const [userRoles, setUserRoles] = useState<UserRoleWithOrganization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize the organization selection function to prevent unnecessary re-renders
  const handleOrganizationSelect = useCallback(async (org: Organization) => {
    try {
      // Convert our simplified Organization to the full type expected by setOrganization
      const fullOrg = {
        id: org.id,
        name: org.name,
        type: org.type,
        settings: org.settings,
        // Add missing properties with default values
        billing_info: {},
        created_at: new Date().toISOString(),
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      };

      // First, make sure we have a user role for this organization
      if (user) {
        // Check if we already have a role for this organization
        const { data: existingRole, error: roleCheckError } = await supabase
          .from('user_roles')
          .select('id')
          .eq('user_id', user.id)
          .eq('organization_id', org.id)
          .maybeSingle();

        if (roleCheckError) {
          console.error('Error checking existing role:', roleCheckError);
        }

        // If no role exists, create one
        if (!existingRole) {
          const { error: roleError } = await supabase
            .from('user_roles')
            .insert([{
              user_id: user.id,
              organization_id: org.id,
              role: 'org_admin' // Default role
            }]);

          if (roleError) {
            console.error('Error creating user role:', roleError);
            throw new Error('Failed to create user role');
          }
        }
      }

      // Set the organization in the auth context
      setOrganization(fullOrg as unknown as ContextOrganization);

      // Show success message
      toast.success(`Switched to ${org.name}`);

      // Navigate to dashboard with replace to prevent back button issues
      navigate('/dashboard', { replace: true });
    } catch (error) {
      console.error('Error switching organization:', error);
      toast.error('Failed to switch organization');
    }
  }, [user, setOrganization, navigate]);

  // Memoize the fetch function to prevent unnecessary re-renders
  const fetchOrganizations = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          role,
          invitation_status,
          organization:organizations (
            id, name, type, settings
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      // Cast the data to unknown first to handle the structure mismatch
      const rawData = data as unknown as Array<{
        role: string;
        invitation_status: string | null;
        organization: {
          id: string;
          name: string;
          type: string;
          settings: Record<string, unknown>;
        }
      }>;

      // Transform the data to match our expected structure
      const transformedData: UserRoleWithOrganization[] = rawData.map(item => ({
        role: item.role,
        invitation_status: item.invitation_status,
        organization: {
          id: item.organization.id,
          name: item.organization.name,
          type: item.organization.type,
          settings: item.organization.settings || {}
        }
      }));

      setUserRoles(transformedData);

      // If there's only one organization and no current organization selected,
      // automatically select it
      if (transformedData.length === 1 && !currentOrg) {
        handleOrganizationSelect(transformedData[0].organization);
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoading(false);
    }
  }, [user, currentOrg, handleOrganizationSelect]);

  // Only run the effect once when the component mounts
  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  const handleCreateOrganization = () => {
    navigate('/setup/organization');
  };

  // Check if the user is an admin for the current organization
  // or if they are a system admin with no organization
  const isAdmin = currentOrg?.id === 'system-admin-no-org' ||
    userRoles.some(
      (role: UserRoleWithOrganization) =>
        role.organization.id === currentOrg?.id &&
        ['system_admin', 'org_admin'].includes(role.role)
    );

  return (
    <div className="container py-8 max-w-5xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Select Organization</h1>
          <p className="text-muted-foreground mt-1">
            Choose an organization to work with or create a new one
          </p>
        </div>
        <Button onClick={handleCreateOrganization}>
          <Plus className="mr-2 h-4 w-4" /> Create Organization
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-sm text-muted-foreground">Loading your organizations...</p>
          </div>
        </div>
      ) : (
        <div className="grid gap-4">
          {userRoles.map(({ organization: org, role, invitation_status }) => (
            <Card
              key={org.id}
              className={`${currentOrg?.id === org.id ? 'border-primary bg-primary/5' : ''} hover:shadow-md transition-all`}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  {org.name}
                  {currentOrg?.id === org.id && (
                    <span className="ml-2 text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                      Current
                    </span>
                  )}
                </CardTitle>
                <div className="flex gap-2">
                  {currentOrg?.id === org.id && isAdmin && (
                    <Button variant="outline" size="sm" onClick={() => navigate(`/organizations/${org.id}/settings`)}>
                      <Settings className="h-4 w-4 mr-1" /> Settings
                    </Button>
                  )}
                  {currentOrg?.id !== org.id ? (
                    <Button variant="default" size="sm" onClick={() => handleOrganizationSelect(org)}>
                      Select
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm" onClick={() => navigate('/dashboard')}>
                      Go to Dashboard
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  <p>Type: {org.type}</p>
                  <p>Role: {role}</p>
                  {invitation_status && invitation_status !== 'accepted' && (
                    <p className="text-yellow-500">Status: {invitation_status}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}

          {userRoles.length === 0 && !isLoading && (
            <div className="text-center py-12 border rounded-lg bg-muted/20">
              <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Organizations Found</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                You don't have any organizations yet. Create your first organization to get started.
              </p>
              <Button onClick={handleCreateOrganization}>
                <Plus className="mr-2 h-4 w-4" /> Create Your First Organization
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
