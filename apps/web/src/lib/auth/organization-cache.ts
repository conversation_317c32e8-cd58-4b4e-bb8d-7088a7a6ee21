import { Organization } from '@/contexts/auth-context-types';

// Cache configuration
// Using a consistent key format that matches what's used in FullSetupPage.tsx
const ORG_CACHE_KEY_PREFIX = 'spritely_org_';
const CACHE_LIFETIME_MS = 24 * 60 * 60 * 1000; // 24 hours - increased for better persistence

// Type for cached organization data
export interface OrganizationCache {
  organization: Organization | null;
  hasOrganization: boolean;
  timestamp: number;
  userId: string;
  lastSelected?: string; // Track the last selected organization ID for multi-tenant users
  availableOrgIds?: string[]; // Store just the IDs of available orgs for multi-org users
}

/**
 * Get the cache key for a specific user
 */
function getUserCacheKey(userId: string): string {
  return `${ORG_CACHE_KEY_PREFIX}${userId}`;
}

/**
 * Save organization data to local cache
 */
export function cacheOrganizationData(
  userId: string,
  organization: Organization | null,
  options?: { isLastSelected?: boolean }
): void {
  try {
    // Get existing cache to preserve lastSelected if needed
    let existingCache: OrganizationCache | null = null;
    try {
      const existingData = localStorage.getItem(getUserCacheKey(userId));
      if (existingData) {
        existingCache = JSON.parse(existingData) as OrganizationCache;
      }
    } catch (e) {
      console.warn('Error parsing existing cache:', e);
    }

    // Create a clean copy of the organization without any circular references
    let cleanOrganization: Organization | null = null;
    if (organization) {
      const { availableOrgs, hasMultipleOrgs, ...baseOrg } = organization as EnhancedOrganization;
      cleanOrganization = baseOrg;
    }

    const cacheData: OrganizationCache = {
      organization: cleanOrganization,
      hasOrganization: !!cleanOrganization,
      timestamp: Date.now(),
      userId,
      // If this is explicitly set as the last selected org, or if we don't have a lastSelected yet
      lastSelected: options?.isLastSelected ? cleanOrganization?.id :
                   (existingCache?.lastSelected || cleanOrganization?.id),
      // Preserve available org IDs
      availableOrgIds: existingCache?.availableOrgIds
    };

    // Use a user-specific cache key
    const cacheKey = getUserCacheKey(userId);
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));

    // Also store the last selected organization ID in a separate key for quick access
    if (cleanOrganization?.id) {
      try {
        localStorage.setItem('spritely_last_org', cleanOrganization.id);
        localStorage.setItem('spritely_last_org_name', cleanOrganization.name);
        localStorage.setItem('spritely_last_org_user', userId);
      } catch (e) {
        console.warn('Error storing last selected organization:', e);
      }
    }

    // Only log in development environment and limit frequency
    if (process.env.NODE_ENV !== 'production' && Math.random() < 0.01) {
      console.debug(`Organization cached for user ${userId}`);
    }
  } catch (error) {
    console.warn('Failed to cache organization data:', error);
  }
}

/**
 * Get organization data from cache if valid
 * @returns Cache data if valid, null otherwise
 */
export function getOrganizationFromCache(userId: string): OrganizationCache | null {
  try {
    // Use a user-specific cache key
    const cacheKey = getUserCacheKey(userId);
    const cachedData = localStorage.getItem(cacheKey);

    if (!cachedData) {
      // Check if we have a last selected organization for this user
      const lastOrgUser = localStorage.getItem('spritely_last_org_user');
      const lastOrgId = localStorage.getItem('spritely_last_org');
      const lastOrgName = localStorage.getItem('spritely_last_org_name');

      if (lastOrgUser === userId && lastOrgId && lastOrgName) {
        // Create a minimal cache entry with the last selected organization
        const minimalCache: OrganizationCache = {
          organization: {
            id: lastOrgId,
            name: lastOrgName,
            type: 'unknown', // We don't have this information
            settings: {},
            billing_info: {},
            created_at: new Date().toISOString(),
            subscription_tier: 'free',
            updated_at: new Date().toISOString(),
            owner_id: userId // Add the required owner_id field
          },
          hasOrganization: true,
          timestamp: Date.now(),
          userId,
          lastSelected: lastOrgId
        };

        // Save this minimal cache for future use
        try {
          localStorage.setItem(cacheKey, JSON.stringify(minimalCache));
        } catch (e) {
          console.warn('Error saving minimal cache:', e);
        }

        return minimalCache;
      }

      return null;
    }

    const cache = JSON.parse(cachedData) as OrganizationCache;

    // Validate cache - must be for current user and not expired
    const isValid =
      cache.userId === userId &&
      (Date.now() - cache.timestamp) < CACHE_LIFETIME_MS;

    if (!isValid) {
      return null;
    }

    return cache;
  } catch (error) {
    // If any error parsing cache, consider it invalid
    console.warn('Failed to retrieve organization cache:', error);
    return null;
  }
}

/**
 * Clear the organization cache for a specific user or all users
 */
export function clearOrganizationCache(userId?: string): void {
  try {
    if (userId) {
      // Clear cache for a specific user
      const cacheKey = getUserCacheKey(userId);
      localStorage.removeItem(cacheKey);

      // Also clear the last selected organization if it belongs to this user
      const lastOrgUser = localStorage.getItem('spritely_last_org_user');
      if (lastOrgUser === userId) {
        localStorage.removeItem('spritely_last_org');
        localStorage.removeItem('spritely_last_org_name');
        localStorage.removeItem('spritely_last_org_user');
      }
    } else {
      // Clear all organization caches by finding all keys with the prefix
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith(ORG_CACHE_KEY_PREFIX) || key.startsWith('spritely_last_org'))) {
          keysToRemove.push(key);
        }
      }

      // Remove all matching keys
      keysToRemove.forEach(key => localStorage.removeItem(key));
    }
  } catch (error) {
    console.warn('Failed to clear organization cache:', error);
  }
}