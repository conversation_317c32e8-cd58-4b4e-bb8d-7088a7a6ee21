import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { useCallback, useEffect, useState } from 'react';

// Cache for user roles to avoid redundant API calls
const userRolesCache: Record<string, {
  roles: string[];
  timestamp: number;
  organizationIds: string[];
}> = {};

// Track in-flight requests to avoid duplicate API calls
const pendingRequests: Record<string, Promise<{
  roles: string[];
  organizationIds: string[];
}>> = {};

// Cache lifetime in milliseconds (5 minutes)
const CACHE_LIFETIME = 5 * 60 * 1000;

export function useUserRoles() {
  const { user } = useAuth();
  const [roles, setRoles] = useState<string[]>([]);
  const [organizationIds, setOrganizationIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchUserRoles = useCallback(async (force = false) => {
    if (!user) {
      setRoles([]);
      setOrganizationIds([]);
      return;
    }

    const userId = user.id;
    const cachedData = userRolesCache[userId];
    const now = Date.now();

    // Use cached data if available and not expired
    if (!force && cachedData && (now - cachedData.timestamp) < CACHE_LIFETIME) {
      setRoles(cachedData.roles);
      setOrganizationIds(cachedData.organizationIds);
      return;
    }

    // Check if there's already a pending request for this user
    if (pendingRequests[userId]) {
      try {
        // Reuse the existing request
        const result = await pendingRequests[userId];
        setRoles(result.roles);
        setOrganizationIds(result.organizationIds);
        return;
      } catch (err) {
        // If the pending request fails, continue with a new request
        delete pendingRequests[userId];
      }
    }

    setIsLoading(true);
    setError(null);

    // Create a new request promise
    const requestPromise = (async () => {
      try {
        const { data, error: apiError } = await supabase
          .from('user_roles')
          .select('role, organization_id')
          .eq('user_id', userId);

        if (apiError) {
          throw new Error(`Failed to fetch user roles: ${apiError.message}`);
        }

        if (data) {
          const rolesList = data.map(item => item.role);
          const orgIds = data
            .map(item => item.organization_id)
            .filter((id): id is string => Boolean(id));

          // Update cache
          userRolesCache[userId] = {
            roles: rolesList,
            organizationIds: orgIds,
            timestamp: now
          };

          // Only log once per session or when forced
          if (force || !window.sessionStorage.getItem(`user_roles_logged_${userId}`)) {
            console.debug(`User roles fetched and cached for user ${userId}:`, {
              roles: rolesList,
              organizationIds: orgIds
            });
            window.sessionStorage.setItem(`user_roles_logged_${userId}`, 'true');
          }

          return { roles: rolesList, organizationIds: orgIds };
        }
        return { roles: [], organizationIds: [] };
      } finally {
        // Clean up the pending request
        delete pendingRequests[userId];
      }
    })();

    // Store the promise
    pendingRequests[userId] = requestPromise;

    try {
      const result = await requestPromise;
      setRoles(result.roles);
      setOrganizationIds(result.organizationIds);
      setIsLoading(false);
    } catch (err) {
      console.error('Error fetching user roles:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching user roles'));
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserRoles();
  }, [fetchUserRoles]);

  const isSystemAdmin = roles.includes('system_admin');
  const isOrgAdmin = roles.includes('org_admin');
  const isAdmin = isSystemAdmin || isOrgAdmin;
  const hasMultipleOrganizations = organizationIds.length > 1;

  return {
    roles,
    organizationIds,
    isLoading,
    error,
    fetchUserRoles,
    isSystemAdmin,
    isOrgAdmin,
    isAdmin,
    hasMultipleOrganizations
  };
}
