import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { useTasks } from "@/hooks/dashboard/useTasks";
import { supabase } from "@/lib/supabase";
import { AlertCircle, Calendar, CheckCircle2, Clock } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function TasksList() {
  const { tasks, isLoading, error, totalCount } = useTasks({ limit: 4 });
  const [completingTaskId, setCompletingTaskId] = useState<string | null>(null);
  const navigate = useNavigate();

  // Function to get priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300';
      case 'medium':
        return 'bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300';
      case 'low':
        return 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Function to handle task completion
  const handleTaskComplete = async (taskId: string) => {
    setCompletingTaskId(taskId);
    
    try {
      const { error: updateError } = await supabase
        .from('tasks')
        .update({ 
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', taskId);

      if (updateError) throw updateError;
      
      toast.success('Task marked as completed');
      
      // Refresh the tasks list
      // In a real app, you'd use a more efficient approach like optimistic UI updates
      window.location.reload();
    } catch (err) {
      console.error('Error completing task:', err);
      toast.error('Failed to complete task');
    } finally {
      setCompletingTaskId(null);
    }
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-red-500">Failed to load tasks</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Tasks</CardTitle>
            <Skeleton className="h-8 w-16" />
          </div>
          <CardDescription>
            <Skeleton className="h-4 w-40" />
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4">
                <Skeleton className="h-5 w-5 rounded-sm" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-full max-w-[250px] mb-1" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 text-xs"
            onClick={() => navigate('/tasks')}
          >
            View All
          </Button>
        </div>
        <CardDescription>
          {totalCount > 0 ? 
            `You have ${totalCount} pending task${totalCount !== 1 ? 's' : ''}` : 
            'No pending tasks'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {tasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 gap-2">
            <CheckCircle2 className="h-8 w-8 text-green-500" />
            <p className="text-sm text-muted-foreground">All caught up! No pending tasks.</p>
          </div>
        ) : (
          <div className="divide-y">
            {tasks.map((task) => (
              <div key={task.id} className="flex items-center gap-4 p-4 hover:bg-muted/50">
                <Checkbox 
                  checked={task.status === 'completed'} 
                  disabled={task.status === 'completed' || !!completingTaskId}
                  onCheckedChange={() => handleTaskComplete(task.id)}
                  className="h-5 w-5"
                />
                <div className="flex-1">
                  <p className={`text-sm font-medium ${task.status === 'completed' ? 'line-through text-muted-foreground' : ''}`}>
                    {task.title}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getPriorityVariant(task.priority)}>
                      {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                    </Badge>
                    {task.due && (
                      <div className="flex items-center text-xs text-muted-foreground">
                        {task.due === 'Today' ? (
                          <AlertCircle className="h-3 w-3 mr-1 text-amber-500" />
                        ) : task.due === 'Tomorrow' ? (
                          <Clock className="h-3 w-3 mr-1 text-blue-500" />
                        ) : (
                          <Calendar className="h-3 w-3 mr-1" />
                        )}
                        {task.due}
                      </div>
                    )}
                    {task.assignee && (
                      <div className="text-xs text-muted-foreground ml-auto">
                        Assigned to: {task.assignee.first_name} {task.assignee.last_name}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
