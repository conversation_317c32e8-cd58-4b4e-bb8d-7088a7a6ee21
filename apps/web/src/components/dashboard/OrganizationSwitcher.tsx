import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Organization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { cacheOrganizationData } from "@/lib/auth/organization-cache";
import { EnhancedOrganization } from "@/lib/auth/organization-types";
import { Building2, ChevronDown, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

export function OrganizationSwitcher() {
  const { organization, setOrganization, user } = useAuth();
  const navigate = useNavigate();
  const [hasMultipleOrgs, setHasMultipleOrgs] = useState(false);
  const [availableOrgs, setAvailableOrgs] = useState<Organization[]>([]);

  // Check if the user has multiple organizations
  useEffect(() => {
    if (organization) {
      const enhancedOrg = organization as EnhancedOrganization;
      setHasMultipleOrgs(!!enhancedOrg.hasMultipleOrgs);
      setAvailableOrgs(enhancedOrg.availableOrgs || []);
    }
  }, [organization]);

  // Handle organization selection from dropdown
  const handleSelectOrganization = async (org: Organization) => {
    if (!user || !org) return;

    try {
      // Set the organization in the auth context
      setOrganization(org);

      // Cache the organization with isLastSelected=true
      cacheOrganizationData(user.id, org, { isLastSelected: true });

      // Refresh the current page to apply the new organization context
      window.location.reload();
    } catch (error) {
      console.error('Error switching organization:', error);
    }
  };

  // Navigate to setup page to create a new organization
  const handleCreateNewOrg = () => {
    navigate('/setup');
  };

  // If there's no organization, show a button to go to setup
  if (!organization) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2"
        onClick={() => navigate('/setup')}
      >
        <Building2 className="h-4 w-4" />
        <span>Select Organization</span>
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {hasMultipleOrgs ? (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Building2 className="h-4 w-4" />
              <span className="max-w-[150px] truncate">{organization.name}</span>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[220px]">
            <DropdownMenuLabel>Your Organizations</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {availableOrgs.map((org) => (
              <DropdownMenuItem
                key={org.id}
                className={`flex items-center gap-2 cursor-pointer ${org.id === organization.id ? 'bg-accent' : ''}`}
                onClick={() => org.id !== organization.id && handleSelectOrganization(org)}
              >
                <Building2 className="h-4 w-4" />
                <span className="flex-1 truncate">{org.name}</span>
                {org.id === organization.id && (
                  <span className="text-xs text-muted-foreground">Current</span>
                )}
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer text-primary"
              onClick={handleCreateNewOrg}
            >
              <Plus className="h-4 w-4" />
              <span>Create New Organization</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <div className="flex items-center gap-2 px-3 py-1 text-sm border rounded-md bg-background">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{organization.name}</span>
        </div>
      )}
    </div>
  );
}
